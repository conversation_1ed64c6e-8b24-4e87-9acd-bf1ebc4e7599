// API service for communicating with the backend

import axios from 'axios';
import {
  ImageData,
  GenerationRequest,
  EditRegionRequest,
  AnalysisResult,
  ThreeDData,
  SceneData,
  UploadResponse,
  HealthStatus,
  ExportOptions,
  ExportResult
} from '../types';

// Configure axios defaults
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000',
  timeout: 300000, // 5 minutes for AI operations
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('❌ API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    console.log(`✅ API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('❌ API Response Error:', error.response?.data || error.message);
    
    // Handle specific error cases
    if (error.response?.status === 404) {
      throw new Error('Resource not found');
    } else if (error.response?.status === 500) {
      throw new Error('Server error occurred');
    } else if (error.code === 'ECONNABORTED') {
      throw new Error('Request timeout - AI processing may take longer');
    }
    
    throw error;
  }
);

export class ApiService {
  
  // Health check
  async healthCheck(): Promise<HealthStatus> {
    const response = await api.get('/api/health');
    return response.data;
  }

  // Image generation
  async generateImage(request: GenerationRequest): Promise<ImageData> {
    const response = await api.post('/api/generate', request);
    return response.data;
  }

  // Image upload
  async uploadImage(file: File): Promise<ImageData> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/api/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    return response.data;
  }

  // Image analysis
  async analyzeImage(imageId: string): Promise<AnalysisResult> {
    const response = await api.post('/api/analyze', { image_id: imageId });
    return response.data;
  }

  // Region editing
  async editRegion(request: EditRegionRequest): Promise<ImageData> {
    const response = await api.post('/api/edit-region', request);
    return response.data;
  }

  // 3D generation
  async generate3D(imageId: string): Promise<ThreeDData> {
    const formData = new FormData();
    formData.append('image_id', imageId);

    const response = await api.post('/api/generate-3d', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    return response.data;
  }

  // Get image info
  async getImageInfo(imageId: string): Promise<ImageData | null> {
    try {
      const response = await api.get(`/api/images/${imageId}`);
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 404) {
        return null;
      }
      throw error;
    }
  }

  // List images
  async listImages(limit: number = 50): Promise<ImageData[]> {
    const response = await api.get(`/api/images?limit=${limit}`);
    return response.data;
  }

  // Delete image
  async deleteImage(imageId: string): Promise<boolean> {
    const response = await api.delete(`/api/images/${imageId}`);
    return response.data.success || true;
  }

  // Export 3D scene
  async exportScene(imageId: string, options: ExportOptions): Promise<ExportResult> {
    const response = await api.post(`/api/export-3d/${imageId}`, options);
    return response.data;
  }

  // Update scene settings
  async updateSceneSettings(imageId: string, settings: any): Promise<SceneData> {
    const response = await api.put(`/api/3d-scene/${imageId}`, settings);
    return response.data;
  }

  // Create custom mask
  async createCustomMask(
    imageId: string, 
    points: Array<{x: number, y: number}>, 
    labels: number[]
  ): Promise<any> {
    const response = await api.post('/api/create-mask', {
      image_id: imageId,
      points,
      labels
    });
    return response.data;
  }

  // Refine mask
  async refineMask(
    maskId: string,
    refinementPoints: Array<{x: number, y: number}>,
    operation: 'add' | 'subtract'
  ): Promise<any> {
    const response = await api.post('/api/refine-mask', {
      mask_id: maskId,
      refinement_points: refinementPoints,
      operation
    });
    return response.data;
  }

  // Batch operations
  async editMultipleRegions(
    imageId: string,
    edits: Array<{region_id: string, prompt: string, strength?: number}>
  ): Promise<ImageData> {
    const response = await api.post('/api/edit-multiple-regions', {
      image_id: imageId,
      region_edits: edits,
      blend_mode: 'sequential'
    });
    return response.data;
  }

  // Download file
  async downloadFile(url: string, filename: string): Promise<void> {
    const response = await api.get(url, {
      responseType: 'blob',
    });

    // Create download link
    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  }

  // Get model status
  async getModelStatus(): Promise<any> {
    const response = await api.get('/api/model-status');
    return response.data;
  }

  // Clear model cache
  async clearModelCache(): Promise<void> {
    await api.post('/api/clear-cache');
  }

  // Get system stats
  async getSystemStats(): Promise<any> {
    const response = await api.get('/api/system-stats');
    return response.data;
  }
}

// Create singleton instance
export const apiService = new ApiService();

// Utility functions for error handling
export const handleApiError = (error: any): string => {
  if (error.response?.data?.detail) {
    return error.response.data.detail;
  } else if (error.message) {
    return error.message;
  } else {
    return 'An unexpected error occurred';
  }
};

export const isNetworkError = (error: any): boolean => {
  return !error.response && error.code !== 'ECONNABORTED';
};

export const isTimeoutError = (error: any): boolean => {
  return error.code === 'ECONNABORTED';
};

// Progress tracking for long operations
export class ProgressTracker {
  private callbacks: Array<(progress: number) => void> = [];
  private currentProgress = 0;

  onProgress(callback: (progress: number) => void) {
    this.callbacks.push(callback);
  }

  updateProgress(progress: number) {
    this.currentProgress = Math.max(0, Math.min(100, progress));
    this.callbacks.forEach(callback => callback(this.currentProgress));
  }

  reset() {
    this.currentProgress = 0;
    this.updateProgress(0);
  }

  complete() {
    this.updateProgress(100);
  }
}

export default api;
