import React, { useRef, useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { ImageData, AnalysisResult, MaskData } from '../types';

interface ImageCanvasProps {
  imageData: ImageData;
  analysisResult: AnalysisResult | null;
  selectedRegion: string | null;
  onSelectRegion: (regionId: string | null) => void;
}

const ImageCanvas: React.FC<ImageCanvasProps> = ({
  imageData,
  analysisResult,
  selectedRegion,
  onSelectRegion
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [hoveredRegion, setHoveredRegion] = useState<string | null>(null);

  // Load and draw image
  useEffect(() => {
    if (!imageData || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    img.onload = () => {
      // Set canvas size to match image
      canvas.width = img.width;
      canvas.height = img.height;
      
      // Draw image
      ctx.drawImage(img, 0, 0);
      setImageLoaded(true);
      
      // Draw regions if available
      if (analysisResult?.segmentation?.masks) {
        drawRegions(ctx, analysisResult.segmentation.masks);
      }
    };

    img.src = imageData.image_url;
    imageRef.current = img;
  }, [imageData, analysisResult]);

  // Redraw regions when selection changes
  useEffect(() => {
    if (!imageLoaded || !canvasRef.current || !imageRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear and redraw image
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.drawImage(imageRef.current, 0, 0);

    // Draw regions
    if (analysisResult?.segmentation?.masks) {
      drawRegions(ctx, analysisResult.segmentation.masks);
    }
  }, [selectedRegion, hoveredRegion, imageLoaded, analysisResult]);

  const drawRegions = (ctx: CanvasRenderingContext2D, masks: MaskData[]) => {
    masks.forEach((mask, index) => {
      const isSelected = selectedRegion === mask.mask_id;
      const isHovered = hoveredRegion === mask.mask_id;
      
      // Draw bounding box
      const [x, y, width, height] = mask.bbox;
      
      ctx.strokeStyle = isSelected 
        ? '#3B82F6' 
        : isHovered 
        ? '#10B981' 
        : 'rgba(255, 255, 255, 0.5)';
      ctx.lineWidth = isSelected ? 3 : isHovered ? 2 : 1;
      ctx.setLineDash(isSelected ? [] : [5, 5]);
      
      ctx.strokeRect(x, y, width, height);
      
      // Draw region overlay
      if (isSelected || isHovered) {
        ctx.fillStyle = isSelected 
          ? 'rgba(59, 130, 246, 0.2)' 
          : 'rgba(16, 185, 129, 0.1)';
        ctx.fillRect(x, y, width, height);
      }
      
      // Draw region label
      if (isSelected || isHovered) {
        const label = `Region ${index + 1}`;
        ctx.fillStyle = isSelected ? '#3B82F6' : '#10B981';
        ctx.font = '12px Inter, sans-serif';
        ctx.fillText(label, x + 5, y + 15);
      }
    });
  };

  const handleCanvasClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!analysisResult?.segmentation?.masks || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;
    
    const x = (event.clientX - rect.left) * scaleX;
    const y = (event.clientY - rect.top) * scaleY;

    // Find clicked region
    const clickedMask = analysisResult.segmentation.masks.find(mask => {
      const [bx, by, bw, bh] = mask.bbox;
      return x >= bx && x <= bx + bw && y >= by && y <= by + bh;
    });

    if (clickedMask) {
      onSelectRegion(
        selectedRegion === clickedMask.mask_id ? null : clickedMask.mask_id
      );
    } else {
      onSelectRegion(null);
    }
  };

  const handleCanvasMouseMove = (event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!analysisResult?.segmentation?.masks || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;
    
    const x = (event.clientX - rect.left) * scaleX;
    const y = (event.clientY - rect.top) * scaleY;

    // Find hovered region
    const hoveredMask = analysisResult.segmentation.masks.find(mask => {
      const [bx, by, bw, bh] = mask.bbox;
      return x >= bx && x <= bx + bw && y >= by && y <= by + bh;
    });

    setHoveredRegion(hoveredMask?.mask_id || null);
  };

  const handleCanvasMouseLeave = () => {
    setHoveredRegion(null);
  };

  return (
    <div className="space-y-4">
      {/* Image Info */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-white">
            Generated Image
          </h3>
          <p className="text-sm text-white/70">
            {imageData.width} × {imageData.height}
            {analysisResult?.segmentation && (
              <span className="ml-2">
                • {analysisResult.segmentation.total_masks} regions detected
              </span>
            )}
          </p>
        </div>
        
        {/* Download Button */}
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => {
            const link = document.createElement('a');
            link.href = imageData.image_url;
            link.download = `generated-image-${imageData.image_id}.png`;
            link.click();
          }}
          className="px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg text-white transition-colors flex items-center space-x-2"
        >
          <span>📥</span>
          <span>Download</span>
        </motion.button>
      </div>

      {/* Canvas Container */}
      <div className="relative bg-black/20 rounded-lg overflow-hidden">
        <canvas
          ref={canvasRef}
          onClick={handleCanvasClick}
          onMouseMove={handleCanvasMouseMove}
          onMouseLeave={handleCanvasMouseLeave}
          className="w-full h-auto cursor-crosshair"
          style={{ maxHeight: '600px', objectFit: 'contain' }}
        />
        
        {/* Loading Overlay */}
        {!imageLoaded && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/50">
            <div className="text-white text-center">
              <div className="loading-spinner mx-auto mb-2"></div>
              <p>Loading image...</p>
            </div>
          </div>
        )}

        {/* Instructions Overlay */}
        {imageLoaded && analysisResult?.segmentation?.masks && (
          <div className="absolute top-4 left-4 bg-black/70 text-white p-3 rounded-lg text-sm">
            <p className="font-medium mb-1">🎯 Interactive Regions</p>
            <p>Click on any region to select and edit it</p>
          </div>
        )}

        {/* Selected Region Info */}
        {selectedRegion && analysisResult?.content_analysis?.regions && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="absolute bottom-4 right-4 bg-blue-500/90 text-white p-3 rounded-lg text-sm max-w-xs"
          >
            {(() => {
              const region = analysisResult.content_analysis.regions.find(
                r => r.mask_id === selectedRegion
              );
              return region ? (
                <div>
                  <p className="font-medium">Selected Region</p>
                  <p>Primary: {region.primary_label}</p>
                  <p>Confidence: {(region.confidence * 100).toFixed(1)}%</p>
                  <p>Area: {region.area.toLocaleString()} pixels</p>
                </div>
              ) : (
                <p>Region selected</p>
              );
            })()}
          </motion.div>
        )}
      </div>

      {/* Image Metadata */}
      {imageData.prompt && (
        <div className="bg-white/5 rounded-lg p-4">
          <h4 className="font-medium text-white mb-2">Original Prompt</h4>
          <p className="text-white/70 text-sm">{imageData.prompt}</p>
          {imageData.negative_prompt && (
            <>
              <h4 className="font-medium text-white mb-2 mt-3">Negative Prompt</h4>
              <p className="text-white/70 text-sm">{imageData.negative_prompt}</p>
            </>
          )}
        </div>
      )}

      {/* Content Analysis Summary */}
      {analysisResult?.content_analysis?.overall_description && (
        <div className="bg-white/5 rounded-lg p-4">
          <h4 className="font-medium text-white mb-2">AI Analysis</h4>
          <div className="space-y-2 text-sm">
            <p className="text-white/70">
              <span className="text-white">Description:</span>{' '}
              {analysisResult.content_analysis.overall_description.generated_description}
            </p>
            <div className="flex flex-wrap gap-2">
              <span className="px-2 py-1 bg-blue-500/20 text-blue-200 rounded text-xs">
                {analysisResult.content_analysis.overall_description.category.label}
              </span>
              <span className="px-2 py-1 bg-purple-500/20 text-purple-200 rounded text-xs">
                {analysisResult.content_analysis.overall_description.style.label}
              </span>
              <span className="px-2 py-1 bg-green-500/20 text-green-200 rounded text-xs">
                {analysisResult.content_analysis.overall_description.mood.label}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageCanvas;
