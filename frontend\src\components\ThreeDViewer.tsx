import React, { useRef, useEffect, useState } from 'react';
import { Canvas, useFrame, useLoader } from '@react-three/fiber';
import { OrbitControls, PerspectiveCamera } from '@react-three/drei';
import * as THREE from 'three';
import { motion } from 'framer-motion';
import { ImageData, ThreeDData } from '../types';

interface ThreeDViewerProps {
  imageData: ImageData;
  threeDData: ThreeDData;
}

// 3D Scene Component
const Scene: React.FC<{ threeDData: ThreeDData; imageUrl: string }> = ({ threeDData, imageUrl }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const [texture, setTexture] = useState<THREE.Texture | null>(null);

  // Load texture
  useEffect(() => {
    const loader = new THREE.TextureLoader();
    loader.load(imageUrl, (loadedTexture) => {
      setTexture(loadedTexture);
    });
  }, [imageUrl]);

  // Create geometry from depth data
  const geometry = React.useMemo(() => {
    const { vertices, faces } = threeDData.three_d_data;
    
    if (!vertices.length) return new THREE.PlaneGeometry(2, 2);

    const geom = new THREE.BufferGeometry();
    
    // Convert vertices to Float32Array
    const vertexArray = new Float32Array(vertices.flat());
    geom.setAttribute('position', new THREE.BufferAttribute(vertexArray, 3));
    
    // Add UV coordinates for texture mapping
    const uvArray = new Float32Array(vertices.length * 2);
    for (let i = 0; i < vertices.length; i++) {
      uvArray[i * 2] = (vertices[i][0] + 1) / 2; // Normalize X to 0-1
      uvArray[i * 2 + 1] = 1 - (vertices[i][1] + 1) / 2; // Normalize Y to 0-1 and flip
    }
    geom.setAttribute('uv', new THREE.BufferAttribute(uvArray, 2));
    
    // Add faces if available
    if (faces.length > 0) {
      const indexArray = new Uint32Array(faces.flat());
      geom.setIndex(new THREE.BufferAttribute(indexArray, 1));
    }
    
    // Compute normals for lighting
    geom.computeVertexNormals();
    
    return geom;
  }, [threeDData]);

  // Animation
  useFrame((state) => {
    if (meshRef.current) {
      // Subtle rotation animation
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.2) * 0.1;
    }
  });

  return (
    <>
      {/* Lighting */}
      <ambientLight intensity={0.4} />
      <directionalLight position={[5, 5, 5]} intensity={0.8} castShadow />
      <pointLight position={[-5, 3, 2]} intensity={0.3} />
      
      {/* 3D Mesh */}
      <mesh ref={meshRef} geometry={geometry} castShadow receiveShadow>
        <meshStandardMaterial
          map={texture}
          roughness={0.7}
          metalness={0.1}
          side={THREE.DoubleSide}
        />
      </mesh>
      
      {/* Ground plane for reference */}
      <mesh position={[0, -1, 0]} rotation={[-Math.PI / 2, 0, 0]} receiveShadow>
        <planeGeometry args={[10, 10]} />
        <meshStandardMaterial color="#333333" transparent opacity={0.3} />
      </mesh>
    </>
  );
};

const ThreeDViewer: React.FC<ThreeDViewerProps> = ({ imageData, threeDData }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showControls, setShowControls] = useState(true);

  useEffect(() => {
    // Simulate loading time
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const handleExport = async (format: 'gltf' | 'obj' | 'ply') => {
    try {
      // This would integrate with the backend export API
      console.log(`Exporting as ${format}...`);
      // await apiService.exportScene(imageData.image_id, { format, include_textures: true, optimize: true });
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="h-96 flex items-center justify-center bg-black/20 rounded-lg">
        <div className="text-center text-white">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="text-4xl mb-4"
          >
            🧭
          </motion.div>
          <p>Loading 3D scene...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-96 flex items-center justify-center bg-red-500/20 rounded-lg border border-red-400/30">
        <div className="text-center text-red-200">
          <div className="text-4xl mb-4">❌</div>
          <p>Failed to load 3D scene</p>
          <p className="text-sm mt-2">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* 3D Viewer Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-white">
            🧭 3D Scene Viewer
          </h3>
          <p className="text-sm text-white/70">
            Interactive 3D visualization with depth mapping
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowControls(!showControls)}
            className="px-3 py-2 bg-white/10 hover:bg-white/20 rounded-lg text-white transition-colors text-sm"
          >
            {showControls ? '🎛️ Hide Controls' : '🎛️ Show Controls'}
          </button>
        </div>
      </div>

      {/* 3D Canvas */}
      <div className="relative h-96 bg-black/20 rounded-lg overflow-hidden">
        <Canvas
          shadows
          camera={{ position: [0, 0, 3], fov: 75 }}
          gl={{ antialias: true, alpha: true }}
        >
          <Scene threeDData={threeDData} imageUrl={imageData.image_url} />
          <OrbitControls
            enableDamping
            dampingFactor={0.05}
            enableZoom
            enableRotate
            enablePan
            maxDistance={10}
            minDistance={0.5}
          />
        </Canvas>

        {/* 3D Controls Overlay */}
        {showControls && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="absolute bottom-4 left-4 bg-black/70 text-white p-3 rounded-lg text-sm"
          >
            <p className="font-medium mb-2">🎮 3D Controls</p>
            <div className="space-y-1 text-xs">
              <p>• Left click + drag: Rotate</p>
              <p>• Right click + drag: Pan</p>
              <p>• Scroll: Zoom in/out</p>
            </div>
          </motion.div>
        )}

        {/* Depth Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="absolute top-4 right-4 bg-black/70 text-white p-3 rounded-lg text-sm"
        >
          <p className="font-medium mb-2">📊 Depth Info</p>
          <div className="space-y-1 text-xs">
            <p>Vertices: {threeDData.three_d_data.vertices.length.toLocaleString()}</p>
            <p>Faces: {threeDData.three_d_data.faces.length.toLocaleString()}</p>
            <p>Depth Range: {threeDData.depth_stats.min_depth.toFixed(2)} - {threeDData.depth_stats.max_depth.toFixed(2)}</p>
          </div>
        </motion.div>
      </div>

      {/* 3D Controls Panel */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        
        {/* Export Options */}
        <div className="glass p-4">
          <h4 className="font-medium text-white mb-3">📤 Export 3D</h4>
          <div className="space-y-2">
            <button
              onClick={() => handleExport('gltf')}
              className="w-full px-3 py-2 bg-blue-500/20 hover:bg-blue-500/30 text-blue-200 rounded-lg transition-colors text-sm"
            >
              Export as GLTF
            </button>
            <button
              onClick={() => handleExport('obj')}
              className="w-full px-3 py-2 bg-green-500/20 hover:bg-green-500/30 text-green-200 rounded-lg transition-colors text-sm"
            >
              Export as OBJ
            </button>
            <button
              onClick={() => handleExport('ply')}
              className="w-full px-3 py-2 bg-purple-500/20 hover:bg-purple-500/30 text-purple-200 rounded-lg transition-colors text-sm"
            >
              Export as PLY
            </button>
          </div>
        </div>

        {/* Depth Visualization */}
        <div className="glass p-4">
          <h4 className="font-medium text-white mb-3">🗺️ Depth Map</h4>
          <div className="aspect-square bg-black/20 rounded-lg overflow-hidden">
            <img
              src={threeDData.depth_viz_url}
              alt="Depth visualization"
              className="w-full h-full object-cover"
            />
          </div>
        </div>

        {/* Scene Statistics */}
        <div className="glass p-4">
          <h4 className="font-medium text-white mb-3">📈 Statistics</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-white/70">Valid Pixels:</span>
              <span className="text-white">{threeDData.depth_stats.valid_pixels.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-white/70">Mean Depth:</span>
              <span className="text-white">{threeDData.depth_stats.mean_depth.toFixed(3)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-white/70">Std Deviation:</span>
              <span className="text-white">{threeDData.depth_stats.std_depth.toFixed(3)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-white/70">Quality:</span>
              <span className={`px-2 py-1 rounded text-xs ${
                threeDData.depth_stats.valid_pixels > 100000
                  ? 'bg-green-500/20 text-green-200'
                  : threeDData.depth_stats.valid_pixels > 50000
                  ? 'bg-yellow-500/20 text-yellow-200'
                  : 'bg-red-500/20 text-red-200'
              }`}>
                {threeDData.depth_stats.valid_pixels > 100000 ? 'High' : 
                 threeDData.depth_stats.valid_pixels > 50000 ? 'Medium' : 'Low'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Tips */}
      <div className="bg-blue-500/20 border border-blue-400/30 rounded-lg p-4">
        <h4 className="font-medium text-blue-200 mb-2">💡 3D Tips</h4>
        <div className="text-sm text-blue-200/80 space-y-1">
          <p>• The 3D effect is generated from depth estimation - some artifacts are normal</p>
          <p>• Try different viewing angles to see the depth effect</p>
          <p>• Export options allow you to use the 3D model in other applications</p>
          <p>• Higher resolution images generally produce better 3D results</p>
        </div>
      </div>
    </div>
  );
};

export default ThreeDViewer;
