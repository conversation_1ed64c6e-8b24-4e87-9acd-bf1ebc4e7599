@echo off
REM Smart Visual Generation System - Windows Startup Script

echo 🚀 Smart Visual Generation System
echo ======================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed. Please install Python 3.9+ first.
    echo Visit: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is not installed. Please install Node.js 18+ first.
    echo Visit: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Python and Node.js are installed

REM Create environment file if it doesn't exist
if not exist .env (
    echo ℹ️ Creating environment file from template...
    copy .env.example .env
    echo ⚠️ Please edit .env file with your API keys and configuration
)

REM Create necessary directories
echo ℹ️ Creating necessary directories...
mkdir static\images 2>nul
mkdir static\masks 2>nul
mkdir static\depth 2>nul
mkdir static\3d 2>nul
mkdir static\comparisons 2>nul
mkdir static\visualizations 2>nul
mkdir static\metadata 2>nul
mkdir models\sam 2>nul
mkdir models\stable_diffusion 2>nul
mkdir models\clip 2>nul
mkdir models\depth 2>nul
mkdir logs 2>nul

REM Setup backend
echo ⚙️ Setting up backend...
cd backend

REM Create virtual environment if it doesn't exist
if not exist venv (
    echo ℹ️ Creating Python virtual environment...
    python -m venv venv
)

REM Activate virtual environment and install dependencies
echo ℹ️ Installing backend dependencies...
call venv\Scripts\activate.bat
python -m pip install --upgrade pip
pip install -r requirements.txt

REM Start backend
echo 🚀 Starting backend...
start "Backend" cmd /k "venv\Scripts\activate.bat && python main.py"

cd ..

REM Setup frontend
echo ⚙️ Setting up frontend...
cd frontend

REM Install dependencies
echo ℹ️ Installing frontend dependencies...
call npm install

REM Start frontend
echo 🚀 Starting frontend...
start "Frontend" cmd /k "npm start"

cd ..

echo.
echo 🎉 Smart Visual Generation System is starting!
echo.
echo 🌐 Frontend (Web UI): http://localhost:3000
echo 🔧 Backend API: http://localhost:8000
echo 📚 API Documentation: http://localhost:8000/docs
echo.
echo ⚠️ First startup may take longer as models are downloaded
echo.
echo Press any key to exit this window (services will continue running)
pause >nul
