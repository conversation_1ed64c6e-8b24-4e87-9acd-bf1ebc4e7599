# Smart Visual Generation System with Semantic Editing and 3D Enhancement

🎨 **An AI-powered creative vision system that combines multiple state-of-the-art models for intelligent image generation, editing, and 3D enhancement.**

## ✨ Features

- **🎨 Stable Diffusion XL**: Generate high-quality images from natural language prompts
- **🔍 SIT-XL2**: Denoise and upscale images for enhanced clarity
- **🧠 SAM (Segment Anything Model)**: Break images into meaningful object regions
- **🗂️ CLIP + GPT-4V**: Intelligent content understanding and labeling
- **✏️ ControlNet Region Editing**: Click and edit any part of an image
- **🧠 GFPGAN + DreamBooth**: Face restoration and celebrity consistency
- **🧭 Depth Estimation**: Convert 2D images to 3D scenes with realistic depth
- **🎥 3D Viewer**: Interactive 3D visualization with parallax effects
- **🎙️ Voice + Touch Editing**: Multi-modal input for seamless editing

## 🚀 Quick Start

### Prerequisites
- Python 3.9+
- Node.js 18+
- NVIDIA GPU (optional, for acceleration)
- 16GB+ RAM
- 50GB+ free disk space (for AI models)

### One-Command Setup

**Linux/macOS:**
```bash
# Make the startup script executable and run it
chmod +x start.sh
./start.sh
```

**Windows:**
```cmd
# Double-click start.bat or run from command prompt
start.bat
```

The script will:
- ✅ Check Python and Node.js installation
- ✅ Create Python virtual environment
- ✅ Install all dependencies automatically
- ✅ Set up environment and directories
- ✅ Optionally download AI models
- ✅ Start backend and frontend services
- ✅ Wait for services to be ready

### Manual Setup (Alternative)

1. **Clone and configure:**
```bash
git clone <repository-url>
cd smart-visual-generation-system
cp .env.example .env
# Edit .env with your API keys (OPENAI_API_KEY, HF_TOKEN)
```

2. **Setup backend:**
```bash
cd backend
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
python main.py
```

3. **Setup frontend (new terminal):**
```bash
cd frontend
npm install
npm start
```

4. **Access the application:**
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Docs: http://localhost:8000/docs

### Environment Configuration

Edit `.env` file with your API keys:
```bash
OPENAI_API_KEY=your_openai_api_key_here
HF_TOKEN=your_huggingface_token_here
```

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │     Backend      │    │   AI Models     │
│   (React)       │◄──►│   (FastAPI)      │◄──►│   Integration   │
│                 │    │                  │    │                 │
│ • Canvas UI     │    │ • Image Service  │    │ • Stable Diff   │
│ • 3D Viewer     │    │ • Edit Service   │    │ • SAM           │
│ • Region Select │    │ • 3D Service     │    │ • CLIP/GPT-4V   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🎯 Core Workflow

1. **Generate**: User enters a prompt → Stable Diffusion XL creates image
2. **Segment**: SAM automatically identifies objects and regions
3. **Understand**: CLIP + GPT-4V analyzes and labels each region
4. **Edit**: User clicks region + new prompt → ControlNet regenerates that area
5. **Enhance**: GFPGAN restores faces, SIT-XL2 upscales quality
6. **3D Convert**: Depth estimation creates 3D scene data
7. **Visualize**: Three.js renders interactive 3D experience

## 🛠️ Technology Stack

**Frontend:**
- React 18 + TypeScript
- Three.js for 3D rendering
- Canvas API for image editing
- Tailwind CSS for styling

**Backend:**
- FastAPI (Python)
- PyTorch for AI models
- OpenCV for image processing
- SQLite/PostgreSQL for data

**AI Models:**
- Stable Diffusion XL
- Segment Anything Model (SAM)
- CLIP + GPT-4V
- ControlNet
- GFPGAN
- MiDaS/ZoeDepth

## 📁 Project Structure

```
├── frontend/           # React application
│   ├── src/
│   │   ├── components/ # UI components
│   │   ├── services/   # API integration
│   │   └── types/      # TypeScript definitions
├── backend/            # FastAPI server
│   ├── models/         # AI model integrations
│   ├── services/       # Business logic
│   └── main.py         # Application entry
├── docker-compose.yml  # Container setup
└── README.md          # This file
```

## 🎮 Usage Examples

### Basic Image Generation
1. Open http://localhost:3000
2. Enter a prompt: "A majestic dragon in a medieval castle"
3. Click "Generate Image"
4. Wait for AI processing (30-60 seconds)
5. View your generated image with automatic segmentation

### Region-Based Editing
1. After generating an image, click on any detected region
2. Enter edit prompt: "Make this dragon blue and breathing ice"
3. Click "Apply Edit"
4. Only the selected region changes while preserving the rest

### 3D Scene Creation
1. Generate or upload an image
2. Click "Generate 3D Scene"
3. Switch to "3D View" to explore the depth-enhanced scene
4. Use mouse to rotate, zoom, and pan
5. Export as GLTF, OBJ, or PLY for use in other applications

### Voice + Touch Editing (Experimental)
1. Click on any object in the image
2. Use voice input: "Make this red"
3. Watch as AI intelligently modifies only that region

## 🛠️ Management Commands

```bash
# Start the system
./start.sh

# Stop the system
./start.sh stop

# Restart services
./start.sh restart

# Check service status
./start.sh status

# Clean generated data and models
./start.sh clean

# Show help
./start.sh help
```

### Development Mode

For development with hot reload:

```bash
# Backend (with auto-reload)
cd backend
source venv/bin/activate
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Frontend (with hot reload)
cd frontend
npm start
```

## 🔧 Configuration

### Key Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `OPENAI_API_KEY` | OpenAI API key for GPT-4V | Yes | - |
| `HF_TOKEN` | Hugging Face token for model downloads | Yes | - |
| `MODEL_CACHE_DIR` | Local model storage directory | No | `./models` |
| `GPU_MEMORY_LIMIT` | GPU memory limit in GB | No | `8` |
| `ENABLE_3D_GENERATION` | Enable 3D scene generation | No | `true` |
| `MAX_UPLOAD_SIZE` | Max file upload size in MB | No | `50` |

### Performance Tuning

For optimal performance:
- **GPU**: NVIDIA RTX 3080+ with 12GB+ VRAM
- **CPU**: 8+ cores for CPU fallback
- **RAM**: 32GB+ recommended for large models
- **Storage**: SSD with 100GB+ free space

### Model Configuration

Models are automatically downloaded on first use:
- **Stable Diffusion XL**: ~13GB
- **SAM (Segment Anything)**: ~2.5GB
- **CLIP**: ~500MB
- **MiDaS Depth**: ~400MB

Total: ~16GB of models

## 🚨 Troubleshooting

### Common Issues

**"CUDA out of memory"**
```bash
# Reduce GPU memory usage in .env
GPU_MEMORY_LIMIT=4
ENABLE_CPU_OFFLOAD=true
```

**"Models not downloading"**
```bash
# Check your Hugging Face token
echo $HF_TOKEN
# Re-run with model download
./start.sh
```

**"Frontend not loading"**
```bash
# Check if backend is running
curl http://localhost:8000/api/health
# Check if Node.js process is running
ps aux | grep "npm start"
# Restart services
./start.sh restart
```

**"Slow generation times"**
- Ensure GPU support is enabled
- Check `nvidia-smi` for GPU usage
- Consider reducing image resolution
- Use fewer inference steps
- Close other GPU-intensive applications

**"Permission denied on start.sh"**
```bash
chmod +x start.sh
```

**"Port already in use"**
```bash
# Kill processes on ports 3000 and 8000
sudo lsof -ti:3000 | xargs kill -9
sudo lsof -ti:8000 | xargs kill -9
```

### Performance Monitoring

```bash
# Check GPU usage
nvidia-smi

# Monitor system resources
htop

# Check Python processes
ps aux | grep python

# Check Node.js processes
ps aux | grep node
```

## 🤝 Contributing

We welcome contributions! Here's how to get started:

1. **Fork the repository**
2. **Create a feature branch**
   ```bash
   git checkout -b feature/amazing-feature
   ```
3. **Make your changes**
   - Follow the existing code style
   - Add tests for new features
   - Update documentation
4. **Test your changes**
   ```bash
   # Run backend tests
   cd backend && python -m pytest

   # Run frontend tests
   cd frontend && npm test
   ```
5. **Submit a Pull Request**
   - Describe your changes clearly
   - Include screenshots for UI changes
   - Reference any related issues

### Development Setup

```bash
# Clone your fork
git clone https://github.com/yourusername/smart-visual-generation.git
cd smart-visual-generation

# Setup backend development environment
cd backend
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
pip install pytest black isort flake8  # Development tools

# Setup frontend development environment
cd ../frontend
npm install
npm install --save-dev @types/jest  # Additional dev dependencies

# Run in development mode with hot reload
./start.sh
```

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📊 System Requirements

### Minimum Requirements
- **OS**: Linux, macOS, or Windows 10+
- **Python**: 3.9+
- **Node.js**: 18+
- **RAM**: 16GB
- **Storage**: 50GB free space
- **GPU**: Optional but recommended (NVIDIA GTX 1060+ or equivalent)

### Recommended Requirements
- **OS**: Ubuntu 20.04+ or macOS 12+
- **Python**: 3.10+
- **Node.js**: 18+
- **RAM**: 32GB+
- **Storage**: 100GB+ SSD
- **GPU**: NVIDIA RTX 3080+ with 12GB+ VRAM
- **CPU**: 8+ cores (Intel i7/AMD Ryzen 7+)

## 🔒 Security & Privacy

- **Local Processing**: All AI processing happens locally by default
- **No Data Collection**: Your images and prompts stay on your machine
- **API Keys**: Only used for optional cloud features (GPT-4V)
- **Open Source**: Full transparency - audit the code yourself

## 📈 Roadmap

### Current Version (v1.0)
- ✅ Stable Diffusion XL generation
- ✅ SAM segmentation
- ✅ CLIP analysis
- ✅ Region-based editing
- ✅ 3D depth estimation
- ✅ Interactive 3D viewer

### Upcoming Features (v1.1)
- 🔄 Real-time editing preview
- 🔄 Batch processing
- 🔄 Advanced 3D effects
- 🔄 Voice input integration
- 🔄 Mobile app companion

### Future Vision (v2.0+)
- 🔮 Video generation and editing
- 🔮 AR/VR integration
- 🔮 Collaborative editing
- 🔮 AI-powered tutorials
- 🔮 Plugin ecosystem

## 🙏 Acknowledgments

This project builds upon incredible work from:

- **Stability AI** - Stable Diffusion models
- **Meta AI** - Segment Anything Model (SAM)
- **OpenAI** - CLIP and GPT-4V
- **Intel** - MiDaS depth estimation
- **Hugging Face** - Model hosting and transformers
- **Three.js** - 3D visualization
- **React** - Frontend framework
- **FastAPI** - Backend framework

Special thanks to the open-source AI community for making this possible! 🎉

## 📞 Support

- **Documentation**: Check this README and inline code comments
- **Issues**: Open a GitHub issue for bugs or feature requests
- **Discussions**: Use GitHub Discussions for questions and ideas
- **Community**: Join our Discord server (link coming soon)

---

<div align="center">

**Made with ❤️ by the AI community**

[⭐ Star this repo](https://github.com/yourusername/smart-visual-generation) • [🐛 Report Bug](https://github.com/yourusername/smart-visual-generation/issues) • [💡 Request Feature](https://github.com/yourusername/smart-visual-generation/issues)

</div>
