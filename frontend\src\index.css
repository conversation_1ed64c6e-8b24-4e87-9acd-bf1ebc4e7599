@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for the Smart Visual Generation System */

@layer base {
  * {
    box-sizing: border-box;
  }
  
  html {
    scroll-behavior: smooth;
  }
  
  body {
    margin: 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
      'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
  }
  
  code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
      monospace;
  }
}

@layer components {
  /* Glass morphism effect */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
  }
  
  /* Primary button style */
  .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: all 0.3s ease;
  }
  
  .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
  }
  
  /* Loading spinner */
  .loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  /* Gradient text */
  .gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  /* Hover lift effect */
  .hover-lift {
    transition: transform 0.2s ease-in-out;
  }
  
  .hover-lift:hover {
    transform: translateY(-2px);
  }
  
  /* Canvas container */
  .canvas-container {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }
  
  /* Region highlight */
  .region-highlight {
    border: 2px solid #667eea;
    border-radius: 4px;
    background: rgba(102, 126, 234, 0.1);
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .region-highlight:hover {
    background: rgba(102, 126, 234, 0.2);
    border-color: #764ba2;
  }
  
  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
  }
}

@layer utilities {
  /* Animation keyframes */
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  @keyframes pulse-glow {
    0%, 100% { 
      box-shadow: 0 0 5px rgba(102, 126, 234, 0.5);
    }
    50% { 
      box-shadow: 0 0 20px rgba(102, 126, 234, 0.8);
    }
  }
  
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }
  
  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.8);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  /* Animation classes */
  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }
  
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  
  .animate-slide-in-up {
    animation: slideInUp 0.5s ease-out;
  }
  
  .animate-slide-in-left {
    animation: slideInLeft 0.5s ease-out;
  }
  
  .animate-slide-in-right {
    animation: slideInRight 0.5s ease-out;
  }
  
  .animate-fade-in {
    animation: fadeIn 0.5s ease-out;
  }
  
  .animate-scale-in {
    animation: scaleIn 0.5s ease-out;
  }
  
  /* Utility classes */
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
  
  .backdrop-blur-strong {
    backdrop-filter: blur(20px);
  }
  
  .border-gradient {
    border: 1px solid;
    border-image: linear-gradient(135deg, #667eea 0%, #764ba2 100%) 1;
  }
  
  /* Responsive utilities */
  .container-fluid {
    width: 100%;
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  @media (min-width: 640px) {
    .container-fluid {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }
  
  @media (min-width: 1024px) {
    .container-fluid {
      padding-left: 4rem;
      padding-right: 4rem;
    }
  }
  
  /* Focus styles */
  .focus-ring:focus {
    outline: none;
    ring: 2px;
    ring-color: rgba(102, 126, 234, 0.5);
    ring-offset: 2px;
  }
  
  /* Selection styles */
  ::selection {
    background: rgba(102, 126, 234, 0.3);
    color: white;
  }
  
  ::-moz-selection {
    background: rgba(102, 126, 234, 0.3);
    color: white;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .auto-dark {
    color-scheme: dark;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .glass {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid white;
  }
  
  .btn-primary {
    background: white;
    color: black;
    border: 2px solid black;
  }
}
