// Zustand store for application state management

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { ImageData, AnalysisResult, AppState } from '../types';

interface AppStore extends AppState {
  // Actions
  setCurrentImage: (image: ImageData | null) => void;
  setSelectedRegion: (regionId: string | null) => void;
  setIsLoading: (loading: boolean) => void;
  setView3D: (view3D: boolean) => void;
  setShowGallery: (show: boolean) => void;
  addImage: (image: ImageData) => void;
  removeImage: (imageId: string) => void;
  updateImage: (imageId: string, updates: Partial<ImageData>) => void;
  clearImages: () => void;
  
  // Computed values
  getImageById: (imageId: string) => ImageData | undefined;
  getRecentImages: (limit?: number) => ImageData[];
  getTotalImages: () => number;
}

export const useAppStore = create<AppStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      currentImage: null,
      selectedRegion: null,
      isLoading: false,
      view3D: false,
      showGallery: false,
      images: [],

      // Actions
      setCurrentImage: (image) => 
        set({ currentImage: image }, false, 'setCurrentImage'),

      setSelectedRegion: (regionId) => 
        set({ selectedRegion: regionId }, false, 'setSelectedRegion'),

      setIsLoading: (loading) => 
        set({ isLoading: loading }, false, 'setIsLoading'),

      setView3D: (view3D) => 
        set({ view3D }, false, 'setView3D'),

      setShowGallery: (show) => 
        set({ showGallery: show }, false, 'setShowGallery'),

      addImage: (image) => 
        set((state) => ({
          images: [image, ...state.images.filter(img => img.image_id !== image.image_id)]
        }), false, 'addImage'),

      removeImage: (imageId) => 
        set((state) => ({
          images: state.images.filter(img => img.image_id !== imageId),
          currentImage: state.currentImage?.image_id === imageId ? null : state.currentImage
        }), false, 'removeImage'),

      updateImage: (imageId, updates) => 
        set((state) => ({
          images: state.images.map(img => 
            img.image_id === imageId ? { ...img, ...updates } : img
          ),
          currentImage: state.currentImage?.image_id === imageId 
            ? { ...state.currentImage, ...updates } 
            : state.currentImage
        }), false, 'updateImage'),

      clearImages: () => 
        set({ images: [], currentImage: null }, false, 'clearImages'),

      // Computed values
      getImageById: (imageId) => 
        get().images.find(img => img.image_id === imageId),

      getRecentImages: (limit = 10) => 
        get().images.slice(0, limit),

      getTotalImages: () => 
        get().images.length,
    }),
    {
      name: 'smart-visual-generation-store',
    }
  )
);
