"""
Smart Visual Generation System - Main FastAPI Application
"""

import os
import logging
from contextlib import asynccontextmanager
from typing import Optional

from fastapi import Fast<PERSON><PERSON>, HTTPException, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import J<PERSON>NResponse
from pydantic import BaseModel
import uvicorn

from services.image_service import ImageService
from services.editing_service import EditingService
from services.three_d_service import ThreeDService
from models.stable_diffusion import StableDiffusionModel
from models.sam_segmentation import SAMModel
from models.clip_analysis import CLIPAnalysisModel
from models.depth_estimation import DepthEstimationModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global services
image_service: Optional[ImageService] = None
editing_service: Optional[EditingService] = None
three_d_service: Optional[ThreeDService] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Initialize and cleanup application resources"""
    global image_service, editing_service, three_d_service
    
    logger.info("🚀 Starting Smart Visual Generation System...")
    
    try:
        # Initialize AI models
        logger.info("📦 Loading AI models...")
        sd_model = StableDiffusionModel()
        sam_model = SAMModel()
        clip_model = CLIPAnalysisModel()
        depth_model = DepthEstimationModel()
        
        # Initialize services
        image_service = ImageService(sd_model, sam_model, clip_model)
        editing_service = EditingService(sd_model, sam_model)
        three_d_service = ThreeDService(depth_model)
        
        logger.info("✅ All models loaded successfully!")
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize models: {e}")
        raise
    
    yield
    
    logger.info("🛑 Shutting down Smart Visual Generation System...")

# Create FastAPI app
app = FastAPI(
    title="Smart Visual Generation System",
    description="AI-powered creative vision system with semantic editing and 3D enhancement",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Serve static files
os.makedirs("static", exist_ok=True)
app.mount("/static", StaticFiles(directory="static"), name="static")

# Pydantic models
class GenerateImageRequest(BaseModel):
    prompt: str
    negative_prompt: Optional[str] = None
    width: int = 1024
    height: int = 1024
    num_inference_steps: int = 50
    guidance_scale: float = 7.5

class EditRegionRequest(BaseModel):
    image_id: str
    region_id: str
    new_prompt: str
    strength: float = 0.8

class AnalyzeImageRequest(BaseModel):
    image_id: str

# API Routes

@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "Smart Visual Generation System is running! 🎨"}

@app.post("/api/generate")
async def generate_image(request: GenerateImageRequest):
    """Generate a new image from text prompt"""
    try:
        logger.info(f"🎨 Generating image: {request.prompt}")
        
        result = await image_service.generate_image(
            prompt=request.prompt,
            negative_prompt=request.negative_prompt,
            width=request.width,
            height=request.height,
            num_inference_steps=request.num_inference_steps,
            guidance_scale=request.guidance_scale
        )
        
        return JSONResponse(content=result)
        
    except Exception as e:
        logger.error(f"❌ Generation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/analyze")
async def analyze_image(request: AnalyzeImageRequest):
    """Analyze image and extract semantic regions"""
    try:
        logger.info(f"🔍 Analyzing image: {request.image_id}")
        
        result = await image_service.analyze_image(request.image_id)
        
        return JSONResponse(content=result)
        
    except Exception as e:
        logger.error(f"❌ Analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/edit-region")
async def edit_region(request: EditRegionRequest):
    """Edit a specific region of an image"""
    try:
        logger.info(f"✏️ Editing region {request.region_id}: {request.new_prompt}")
        
        result = await editing_service.edit_region(
            image_id=request.image_id,
            region_id=request.region_id,
            new_prompt=request.new_prompt,
            strength=request.strength
        )
        
        return JSONResponse(content=result)
        
    except Exception as e:
        logger.error(f"❌ Region editing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/generate-3d")
async def generate_3d(image_id: str = Form(...)):
    """Generate 3D depth map and scene data"""
    try:
        logger.info(f"🧭 Generating 3D data for image: {image_id}")
        
        result = await three_d_service.generate_3d_scene(image_id)
        
        return JSONResponse(content=result)
        
    except Exception as e:
        logger.error(f"❌ 3D generation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/upload")
async def upload_image(file: UploadFile = File(...)):
    """Upload an existing image for editing"""
    try:
        logger.info(f"📤 Uploading image: {file.filename}")
        
        # Validate file type
        if not file.content_type.startswith("image/"):
            raise HTTPException(status_code=400, detail="File must be an image")
        
        result = await image_service.upload_image(file)
        
        return JSONResponse(content=result)
        
    except Exception as e:
        logger.error(f"❌ Upload failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/images/{image_id}")
async def get_image_info(image_id: str):
    """Get information about a specific image"""
    try:
        result = await image_service.get_image_info(image_id)
        
        if not result:
            raise HTTPException(status_code=404, detail="Image not found")
        
        return JSONResponse(content=result)
        
    except Exception as e:
        logger.error(f"❌ Failed to get image info: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/health")
async def health_check():
    """Detailed health check with model status"""
    try:
        status = {
            "status": "healthy",
            "models": {
                "stable_diffusion": image_service.sd_model.is_loaded if image_service else False,
                "sam": image_service.sam_model.is_loaded if image_service else False,
                "clip": image_service.clip_model.is_loaded if image_service else False,
                "depth": three_d_service.depth_model.is_loaded if three_d_service else False,
            }
        }
        
        return JSONResponse(content=status)
        
    except Exception as e:
        logger.error(f"❌ Health check failed: {e}")
        return JSONResponse(
            status_code=500,
            content={"status": "unhealthy", "error": str(e)}
        )

if __name__ == "__main__":
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Run the application
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
