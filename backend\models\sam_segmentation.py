"""
Segment Anything Model (SAM) Integration
Automatic image segmentation for region-based editing
"""

import os
import cv2
import torch
import logging
import numpy as np
from typing import List, Dict, Any, Tuple
from PIL import Image

try:
    from segment_anything import sam_model_registry, SamAutomaticMaskGenerator, SamPredictor
except ImportError:
    logger.warning("⚠️ Segment Anything not installed. Install with: pip install git+https://github.com/facebookresearch/segment-anything.git")

logger = logging.getLogger(__name__)

class SAMModel:
    """Segment Anything Model for automatic image segmentation"""
    
    def __init__(self):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.sam = None
        self.mask_generator = None
        self.predictor = None
        self.is_loaded = False
        
        # Model configuration
        self.model_type = "vit_h"  # or "vit_l", "vit_b"
        self.checkpoint_path = self._get_checkpoint_path()
        
        logger.info(f"🧠 Initializing SAM on {self.device}")
        self._load_model()
    
    def _get_checkpoint_path(self) -> str:
        """Get or download SAM checkpoint"""
        checkpoint_dir = "models/sam"
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        checkpoint_file = f"sam_{self.model_type}.pth"
        checkpoint_path = os.path.join(checkpoint_dir, checkpoint_file)
        
        if not os.path.exists(checkpoint_path):
            logger.info(f"📥 Downloading SAM checkpoint: {checkpoint_file}")
            # Download checkpoint
            import urllib.request
            
            urls = {
                "vit_h": "https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth",
                "vit_l": "https://dl.fbaipublicfiles.com/segment_anything/sam_vit_l_0b3195.pth",
                "vit_b": "https://dl.fbaipublicfiles.com/segment_anything/sam_vit_b_01ec64.pth"
            }
            
            try:
                urllib.request.urlretrieve(urls[self.model_type], checkpoint_path)
                logger.info(f"✅ Downloaded {checkpoint_file}")
            except Exception as e:
                logger.error(f"❌ Failed to download SAM checkpoint: {e}")
                raise
        
        return checkpoint_path
    
    def _load_model(self):
        """Load the SAM model"""
        try:
            logger.info("📦 Loading SAM model...")
            
            # Load SAM model
            self.sam = sam_model_registry[self.model_type](checkpoint=self.checkpoint_path)
            self.sam.to(device=self.device)
            
            # Initialize mask generator for automatic segmentation
            self.mask_generator = SamAutomaticMaskGenerator(
                model=self.sam,
                points_per_side=32,
                pred_iou_thresh=0.86,
                stability_score_thresh=0.92,
                crop_n_layers=1,
                crop_n_points_downscale_factor=2,
                min_mask_region_area=100,
            )
            
            # Initialize predictor for interactive segmentation
            self.predictor = SamPredictor(self.sam)
            
            self.is_loaded = True
            logger.info("✅ SAM model loaded successfully!")
            
        except Exception as e:
            logger.error(f"❌ Failed to load SAM: {e}")
            self.is_loaded = False
            raise
    
    async def segment_image(self, image: Image.Image) -> Dict[str, Any]:
        """Automatically segment an image into regions"""
        
        if not self.is_loaded:
            raise RuntimeError("SAM model not loaded")
        
        try:
            logger.info("🔍 Segmenting image...")
            
            # Convert PIL to numpy array
            image_array = np.array(image)
            if image_array.shape[-1] == 4:  # RGBA
                image_array = image_array[:, :, :3]  # Remove alpha channel
            
            # Generate masks
            masks = self.mask_generator.generate(image_array)
            
            # Sort masks by area (largest first)
            masks = sorted(masks, key=lambda x: x['area'], reverse=True)
            
            # Process masks
            processed_masks = []
            for i, mask_data in enumerate(masks[:50]):  # Limit to top 50 masks
                mask = mask_data['segmentation']
                
                # Calculate mask properties
                bbox = mask_data['bbox']  # [x, y, width, height]
                area = mask_data['area']
                stability_score = mask_data['stability_score']
                predicted_iou = mask_data['predicted_iou']
                
                # Create mask image
                mask_image = Image.fromarray((mask * 255).astype(np.uint8), mode='L')
                
                # Save mask
                mask_id = f"mask_{i:03d}"
                mask_path = f"static/masks/{mask_id}.png"
                os.makedirs(os.path.dirname(mask_path), exist_ok=True)
                mask_image.save(mask_path)
                
                processed_masks.append({
                    "mask_id": mask_id,
                    "mask_url": f"/static/masks/{mask_id}.png",
                    "bbox": bbox,
                    "area": int(area),
                    "stability_score": float(stability_score),
                    "predicted_iou": float(predicted_iou),
                    "center": [int(bbox[0] + bbox[2]/2), int(bbox[1] + bbox[3]/2)]
                })
            
            # Create combined visualization
            viz_image = self._create_segmentation_visualization(image_array, masks[:20])
            viz_path = "static/visualizations/segmentation.png"
            os.makedirs(os.path.dirname(viz_path), exist_ok=True)
            viz_image.save(viz_path)
            
            return {
                "masks": processed_masks,
                "total_masks": len(masks),
                "visualization_url": "/static/visualizations/segmentation.png"
            }
            
        except Exception as e:
            logger.error(f"❌ Segmentation failed: {e}")
            raise
    
    async def segment_from_points(
        self, 
        image: Image.Image, 
        points: List[Tuple[int, int]], 
        labels: List[int]
    ) -> Dict[str, Any]:
        """Segment image based on user-provided points"""
        
        if not self.is_loaded:
            raise RuntimeError("SAM model not loaded")
        
        try:
            logger.info(f"🎯 Segmenting from {len(points)} points...")
            
            # Convert PIL to numpy array
            image_array = np.array(image)
            if image_array.shape[-1] == 4:  # RGBA
                image_array = image_array[:, :, :3]
            
            # Set image for predictor
            self.predictor.set_image(image_array)
            
            # Convert points and labels to numpy arrays
            input_points = np.array(points)
            input_labels = np.array(labels)
            
            # Predict masks
            masks, scores, logits = self.predictor.predict(
                point_coords=input_points,
                point_labels=input_labels,
                multimask_output=True,
            )
            
            # Select best mask
            best_mask_idx = np.argmax(scores)
            best_mask = masks[best_mask_idx]
            best_score = scores[best_mask_idx]
            
            # Create mask image
            mask_image = Image.fromarray((best_mask * 255).astype(np.uint8), mode='L')
            
            # Save mask
            mask_id = f"interactive_mask_{hash(str(points))}"
            mask_path = f"static/masks/{mask_id}.png"
            os.makedirs(os.path.dirname(mask_path), exist_ok=True)
            mask_image.save(mask_path)
            
            return {
                "mask_id": mask_id,
                "mask_url": f"/static/masks/{mask_id}.png",
                "score": float(best_score),
                "points": points,
                "labels": labels
            }
            
        except Exception as e:
            logger.error(f"❌ Interactive segmentation failed: {e}")
            raise
    
    def _create_segmentation_visualization(self, image: np.ndarray, masks: List[Dict]) -> Image.Image:
        """Create a visualization of the segmentation results"""
        
        # Create overlay
        overlay = image.copy()
        
        # Generate colors for masks
        colors = self._generate_colors(len(masks))
        
        for mask_data, color in zip(masks, colors):
            mask = mask_data['segmentation']
            
            # Apply colored mask
            colored_mask = np.zeros_like(image)
            colored_mask[mask] = color
            
            # Blend with original image
            alpha = 0.3
            overlay = cv2.addWeighted(overlay, 1-alpha, colored_mask, alpha, 0)
            
            # Draw contours
            contours, _ = cv2.findContours(
                mask.astype(np.uint8), 
                cv2.RETR_EXTERNAL, 
                cv2.CHAIN_APPROX_SIMPLE
            )
            cv2.drawContours(overlay, contours, -1, color, 2)
        
        return Image.fromarray(overlay)
    
    def _generate_colors(self, n: int) -> List[Tuple[int, int, int]]:
        """Generate n distinct colors"""
        colors = []
        for i in range(n):
            hue = (i * 137.508) % 360  # Golden angle approximation
            saturation = 0.7
            value = 0.9
            
            # Convert HSV to RGB
            import colorsys
            rgb = colorsys.hsv_to_rgb(hue/360, saturation, value)
            colors.append(tuple(int(c * 255) for c in rgb))
        
        return colors
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model"""
        return {
            "model_type": self.model_type,
            "checkpoint_path": self.checkpoint_path,
            "device": self.device,
            "is_loaded": self.is_loaded
        }
