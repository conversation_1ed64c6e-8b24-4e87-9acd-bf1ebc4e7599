"""
Editing Service - Region-based image editing
"""

import os
import logging
import numpy as np
from typing import Dict, Any, Optional
from PIL import Image, ImageDraw

logger = logging.getLogger(__name__)

class EditingService:
    """Service for region-based image editing using AI models"""
    
    def __init__(self, sd_model, sam_model):
        self.sd_model = sd_model
        self.sam_model = sam_model
        
        logger.info("✏️ Editing Service initialized")
    
    async def edit_region(
        self,
        image_id: str,
        region_id: str,
        new_prompt: str,
        strength: float = 0.8
    ) -> Dict[str, Any]:
        """Edit a specific region of an image"""
        
        try:
            logger.info(f"✏️ Editing region {region_id} with prompt: {new_prompt}")
            
            # Load original image
            image_path = f"static/images/{image_id}.png"
            if not os.path.exists(image_path):
                raise FileNotFoundError(f"Image {image_id} not found")
            
            original_image = Image.open(image_path)
            
            # Load region mask
            mask_path = f"static/masks/{region_id}.png"
            if not os.path.exists(mask_path):
                raise FileNotFoundError(f"Mask {region_id} not found")
            
            mask_image = Image.open(mask_path).convert('L')
            
            # Ensure images are the same size
            if original_image.size != mask_image.size:
                mask_image = mask_image.resize(original_image.size, Image.Resampling.LANCZOS)
            
            # Perform inpainting
            edited_image = await self.sd_model.inpaint_region(
                image=original_image,
                mask=mask_image,
                prompt=new_prompt,
                strength=strength
            )
            
            # Generate new image ID for edited version
            edited_id = self._generate_edited_id(image_id, region_id)
            edited_path = f"static/images/{edited_id}.png"
            edited_image.save(edited_path)
            
            # Create comparison visualization
            comparison_viz = self._create_comparison_visualization(
                original_image, edited_image, mask_image
            )
            comparison_path = f"static/comparisons/{edited_id}_comparison.png"
            os.makedirs(os.path.dirname(comparison_path), exist_ok=True)
            comparison_viz.save(comparison_path)
            
            return {
                "image_id": edited_id,
                "image_url": f"/static/images/{edited_id}.png",
                "comparison_url": f"/static/comparisons/{edited_id}_comparison.png",
                "original_image_id": image_id,
                "edited_region": region_id,
                "edit_prompt": new_prompt,
                "strength": strength,
                "status": "edited"
            }
            
        except Exception as e:
            logger.error(f"❌ Region editing failed: {e}")
            raise
    
    async def edit_multiple_regions(
        self,
        image_id: str,
        region_edits: list,
        blend_mode: str = "sequential"
    ) -> Dict[str, Any]:
        """Edit multiple regions in a single image"""
        
        try:
            logger.info(f"✏️ Editing {len(region_edits)} regions")
            
            current_image_id = image_id
            edit_history = []
            
            for i, edit in enumerate(region_edits):
                region_id = edit["region_id"]
                new_prompt = edit["prompt"]
                strength = edit.get("strength", 0.8)
                
                # Edit current region
                result = await self.edit_region(
                    image_id=current_image_id,
                    region_id=region_id,
                    new_prompt=new_prompt,
                    strength=strength
                )
                
                edit_history.append(result)
                current_image_id = result["image_id"]
            
            return {
                "final_image_id": current_image_id,
                "final_image_url": f"/static/images/{current_image_id}.png",
                "edit_history": edit_history,
                "total_edits": len(region_edits),
                "status": "multi_edited"
            }
            
        except Exception as e:
            logger.error(f"❌ Multiple region editing failed: {e}")
            raise
    
    async def create_custom_mask(
        self,
        image_id: str,
        points: list,
        labels: list
    ) -> Dict[str, Any]:
        """Create a custom mask from user-provided points"""
        
        try:
            logger.info(f"🎯 Creating custom mask from {len(points)} points")
            
            # Load image
            image_path = f"static/images/{image_id}.png"
            if not os.path.exists(image_path):
                raise FileNotFoundError(f"Image {image_id} not found")
            
            image = Image.open(image_path)
            
            # Use SAM to generate mask from points
            mask_result = await self.sam_model.segment_from_points(
                image=image,
                points=points,
                labels=labels
            )
            
            return mask_result
            
        except Exception as e:
            logger.error(f"❌ Custom mask creation failed: {e}")
            raise
    
    async def refine_mask(
        self,
        mask_id: str,
        refinement_points: list,
        operation: str = "add"  # "add" or "subtract"
    ) -> Dict[str, Any]:
        """Refine an existing mask by adding or subtracting regions"""
        
        try:
            logger.info(f"🔧 Refining mask {mask_id}")
            
            # Load existing mask
            mask_path = f"static/masks/{mask_id}.png"
            if not os.path.exists(mask_path):
                raise FileNotFoundError(f"Mask {mask_id} not found")
            
            existing_mask = Image.open(mask_path).convert('L')
            mask_array = np.array(existing_mask)
            
            # Create refinement mask from points
            refinement_mask = self._create_mask_from_points(
                existing_mask.size, refinement_points
            )
            
            # Apply refinement
            if operation == "add":
                refined_array = np.maximum(mask_array, refinement_mask)
            elif operation == "subtract":
                refined_array = np.where(refinement_mask > 0, 0, mask_array)
            else:
                raise ValueError(f"Unknown operation: {operation}")
            
            # Save refined mask
            refined_id = f"{mask_id}_refined"
            refined_path = f"static/masks/{refined_id}.png"
            refined_mask = Image.fromarray(refined_array.astype(np.uint8))
            refined_mask.save(refined_path)
            
            return {
                "mask_id": refined_id,
                "mask_url": f"/static/masks/{refined_id}.png",
                "original_mask_id": mask_id,
                "operation": operation,
                "refinement_points": refinement_points
            }
            
        except Exception as e:
            logger.error(f"❌ Mask refinement failed: {e}")
            raise
    
    def _create_comparison_visualization(
        self,
        original: Image.Image,
        edited: Image.Image,
        mask: Image.Image
    ) -> Image.Image:
        """Create a side-by-side comparison visualization"""
        
        # Ensure all images are the same size
        size = original.size
        edited = edited.resize(size, Image.Resampling.LANCZOS)
        mask = mask.resize(size, Image.Resampling.LANCZOS)
        
        # Create comparison image (original | mask | edited)
        comparison_width = size[0] * 3
        comparison_height = size[1]
        comparison = Image.new('RGB', (comparison_width, comparison_height), (255, 255, 255))
        
        # Paste original image
        comparison.paste(original, (0, 0))
        
        # Create mask overlay
        mask_overlay = Image.new('RGBA', size, (255, 0, 0, 128))
        mask_alpha = Image.new('L', size)
        mask_alpha.paste(mask)
        mask_overlay.putalpha(mask_alpha)
        
        # Paste mask overlay on original
        original_with_mask = original.copy().convert('RGBA')
        original_with_mask = Image.alpha_composite(original_with_mask, mask_overlay)
        comparison.paste(original_with_mask.convert('RGB'), (size[0], 0))
        
        # Paste edited image
        comparison.paste(edited, (size[0] * 2, 0))
        
        # Add labels
        draw = ImageDraw.Draw(comparison)
        try:
            # Try to use a better font if available
            from PIL import ImageFont
            font = ImageFont.truetype("arial.ttf", 20)
        except:
            font = None
        
        draw.text((10, 10), "Original", fill=(255, 255, 255), font=font)
        draw.text((size[0] + 10, 10), "Mask", fill=(255, 255, 255), font=font)
        draw.text((size[0] * 2 + 10, 10), "Edited", fill=(255, 255, 255), font=font)
        
        return comparison
    
    def _create_mask_from_points(self, size: tuple, points: list) -> np.ndarray:
        """Create a mask from a list of points"""
        
        mask = np.zeros((size[1], size[0]), dtype=np.uint8)
        
        if not points:
            return mask
        
        # Create a simple circular mask around each point
        for point in points:
            x, y = point
            radius = 10  # Fixed radius for now
            
            # Create circular mask
            y_coords, x_coords = np.ogrid[:size[1], :size[0]]
            distance = np.sqrt((x_coords - x)**2 + (y_coords - y)**2)
            mask[distance <= radius] = 255
        
        return mask
    
    def _generate_edited_id(self, original_id: str, region_id: str) -> str:
        """Generate a unique ID for edited image"""
        import uuid
        return f"{original_id}_edit_{region_id}_{str(uuid.uuid4())[:8]}"
