# Smart Visual Generation System - Environment Configuration

# =============================================================================
# API Configuration
# =============================================================================

# OpenAI API Key (for GPT-4V integration)
OPENAI_API_KEY=********************************************************************************************************************************************************************

# Hugging Face Token (for model downloads)
HF_TOKEN=*************************************

# =============================================================================
# Model Configuration
# =============================================================================

# Local model cache directory
MODEL_CACHE_DIR=./models

# GPU memory limit (in GB, set to 0 for CPU-only)
GPU_MEMORY_LIMIT=8

# Model precision (float16, float32)
MODEL_PRECISION=float16

# Enable model CPU offloading to save GPU memory
ENABLE_CPU_OFFLOAD=true

# =============================================================================
# Stable Diffusion Configuration
# =============================================================================

# Stable Diffusion model ID
SD_MODEL_ID=stabilityai/stable-diffusion-xl-base-1.0

# Stable Diffusion refiner model ID
SD_REFINER_ID=stabilityai/stable-diffusion-xl-refiner-1.0

# Default image generation settings
DEFAULT_WIDTH=1024
DEFAULT_HEIGHT=1024
DEFAULT_STEPS=50
DEFAULT_GUIDANCE_SCALE=7.5

# =============================================================================
# SAM (Segment Anything Model) Configuration
# =============================================================================

# SAM model type (vit_h, vit_l, vit_b)
SAM_MODEL_TYPE=vit_h

# SAM checkpoint URL (auto-downloaded if not present)
SAM_CHECKPOINT_URL=https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth

# Segmentation quality settings
SAM_POINTS_PER_SIDE=32
SAM_PRED_IOU_THRESH=0.86
SAM_STABILITY_SCORE_THRESH=0.92

# =============================================================================
# CLIP Configuration
# =============================================================================

# CLIP model name
CLIP_MODEL_NAME=ViT-B/32

# Enable CLIP analysis
ENABLE_CLIP_ANALYSIS=true

# =============================================================================
# Depth Estimation Configuration
# =============================================================================

# Depth model type (MiDaS, ZoeDepth)
DEPTH_MODEL_TYPE=MiDaS

# Enable 3D scene generation
ENABLE_3D_GENERATION=true

# =============================================================================
# Storage Configuration
# =============================================================================

# Static files directory
STATIC_DIR=./static

# Maximum file upload size (in MB)
MAX_UPLOAD_SIZE=50

# Image quality for saved files (1-100)
IMAGE_QUALITY=95

# =============================================================================
# Database Configuration
# =============================================================================

# Database URL (SQLite by default)
DATABASE_URL=sqlite:///./smart_visual_generation.db

# Enable database migrations
AUTO_MIGRATE=true

# =============================================================================
# Server Configuration
# =============================================================================

# Server host and port
HOST=0.0.0.0
PORT=8000

# Enable debug mode
DEBUG=false

# Enable hot reload in development
RELOAD=true

# Log level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# =============================================================================
# Security Configuration
# =============================================================================

# Secret key for session management
SECRET_KEY=your_secret_key_here

# CORS allowed origins (comma-separated)
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Enable HTTPS redirect
FORCE_HTTPS=false

# =============================================================================
# Performance Configuration
# =============================================================================

# Number of worker processes
WORKERS=1

# Request timeout (in seconds)
REQUEST_TIMEOUT=300

# Enable response compression
ENABLE_COMPRESSION=true

# Cache TTL (in seconds)
CACHE_TTL=3600

# =============================================================================
# Feature Flags
# =============================================================================

# Enable experimental features
ENABLE_EXPERIMENTAL=false

# Enable face restoration (GFPGAN)
ENABLE_FACE_RESTORATION=true

# Enable image upscaling (Real-ESRGAN)
ENABLE_UPSCALING=true

# Enable voice input (experimental)
ENABLE_VOICE_INPUT=false

# Enable batch processing
ENABLE_BATCH_PROCESSING=true

# =============================================================================
# Monitoring and Analytics
# =============================================================================

# Enable performance monitoring
ENABLE_MONITORING=false

# Analytics endpoint
ANALYTICS_ENDPOINT=

# Error reporting service
ERROR_REPORTING_DSN=

# =============================================================================
# External Services (Optional)
# =============================================================================

# Redis URL (for caching and queues) - Optional
# REDIS_URL=redis://localhost:6379

# Email service configuration (Optional)
# SMTP_HOST=
# SMTP_PORT=587
# SMTP_USER=
# SMTP_PASSWORD=
# SMTP_TLS=true

# =============================================================================
# Development Configuration
# =============================================================================

# Enable development tools
DEV_TOOLS=true

# Enable API documentation
ENABLE_DOCS=true

# Enable interactive API explorer
ENABLE_REDOC=true

# Profiling mode
ENABLE_PROFILING=false

# =============================================================================
# Production Configuration
# =============================================================================

# Production optimizations
PRODUCTION_MODE=false

# Enable request rate limiting
ENABLE_RATE_LIMITING=false

# Rate limit (requests per minute)
RATE_LIMIT=60

# Enable request logging
ENABLE_REQUEST_LOGGING=true

# =============================================================================
# Cloud Storage (Optional)
# =============================================================================

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_S3_BUCKET=
AWS_S3_REGION=us-east-1

# Google Cloud Storage
GCS_BUCKET=
GCS_CREDENTIALS_PATH=

# Azure Blob Storage
AZURE_STORAGE_ACCOUNT=
AZURE_STORAGE_KEY=
AZURE_CONTAINER=

# =============================================================================
# Backup Configuration
# =============================================================================

# Enable automatic backups
ENABLE_BACKUPS=false

# Backup interval (in hours)
BACKUP_INTERVAL=24

# Backup retention (in days)
BACKUP_RETENTION=30

# Backup storage path
BACKUP_PATH=./backups
