// Type definitions for the Smart Visual Generation System

export interface ImageData {
  image_id: string;
  image_url: string;
  prompt?: string;
  negative_prompt?: string;
  width: number;
  height: number;
  seed?: number;
  guidance_scale?: number;
  num_inference_steps?: number;
  analysis?: AnalysisResult;
  three_d_data?: ThreeDData;
  status: string;
  original_filename?: string;
  file_info?: {
    size_bytes: number;
    created: number;
    modified: number;
  };
}

export interface GenerationRequest {
  prompt: string;
  negative_prompt?: string;
  width?: number;
  height?: number;
  num_inference_steps?: number;
  guidance_scale?: number;
  seed?: number;
}

export interface EditRegionRequest {
  image_id: string;
  region_id: string;
  new_prompt: string;
  strength?: number;
}

export interface AnalysisResult {
  image_id: string;
  dimensions: {
    width: number;
    height: number;
  };
  segmentation?: SegmentationResult;
  content_analysis?: ContentAnalysis;
}

export interface SegmentationResult {
  masks: MaskData[];
  total_masks: number;
  visualization_url: string;
}

export interface MaskData {
  mask_id: string;
  mask_url: string;
  bbox: [number, number, number, number]; // [x, y, width, height]
  area: number;
  stability_score: number;
  predicted_iou: number;
  center: [number, number];
}

export interface ContentAnalysis {
  overall_description: {
    category: {
      label: string;
      confidence: number;
    };
    style: {
      label: string;
      confidence: number;
    };
    mood: {
      label: string;
      confidence: number;
    };
    generated_description: string;
  };
  regions: RegionAnalysis[];
  total_regions: number;
  confidence_scores: {
    overall_confidence: number;
    feature_magnitude: number;
    analysis_quality: string;
  };
}

export interface RegionAnalysis {
  mask_id: string;
  bbox: [number, number, number, number];
  area: number;
  predictions: {
    label: string;
    confidence: number;
  }[];
  primary_label: string;
  confidence: number;
}

export interface ThreeDData {
  depth_id: string;
  depth_map_url: string;
  depth_viz_url: string;
  point_cloud_url: string;
  depth_stats: {
    min_depth: number;
    max_depth: number;
    mean_depth: number;
    std_depth: number;
    valid_pixels: number;
  };
  three_d_data: {
    vertices: number[][];
    colors: number[][];
    faces: number[][];
    depth_range: {
      min: number;
      max: number;
      mean: number;
    };
  };
}

export interface SceneData {
  image_id: string;
  depth_data: ThreeDData;
  scene_config: {
    geometry: {
      vertices: number[][];
      colors: number[][];
      faces: number[][];
      vertex_count: number;
      face_count: number;
    };
    textures: {
      diffuse_map: string;
      depth_map: string;
      depth_visualization: string;
    };
    materials: {
      base_material: {
        type: string;
        roughness: number;
        metalness: number;
        transparent: boolean;
      };
    };
    animations: {
      parallax_enabled: boolean;
      rotation_enabled: boolean;
      zoom_enabled: boolean;
      auto_rotate: boolean;
      rotation_speed: number;
    };
  };
  camera_settings: CameraSettings;
  lighting_setup: LightingSetup;
  metadata: {
    image_dimensions: [number, number];
    depth_quality: any;
    generation_timestamp: string;
  };
}

export interface CameraSettings {
  type: string;
  fov: number;
  aspect: number;
  near: number;
  far: number;
  position: {
    x: number;
    y: number;
    z: number;
  };
  target: {
    x: number;
    y: number;
    z: number;
  };
  controls: {
    enabled: boolean;
    enableDamping: boolean;
    dampingFactor: number;
    enableZoom: boolean;
    enableRotate: boolean;
    enablePan: boolean;
    maxDistance: number;
    minDistance: number;
    maxPolarAngle: number;
    minPolarAngle: number;
  };
}

export interface LightingSetup {
  ambient_light: {
    type: string;
    color: number;
    intensity: number;
  };
  directional_light: {
    type: string;
    color: number;
    intensity: number;
    position: {
      x: number;
      y: number;
      z: number;
    };
    cast_shadow: boolean;
    shadow: {
      map_size: number;
      camera: {
        near: number;
        far: number;
        left: number;
        right: number;
        top: number;
        bottom: number;
      };
    };
  };
  point_lights: Array<{
    type: string;
    color: number;
    intensity: number;
    position: {
      x: number;
      y: number;
      z: number;
    };
    distance: number;
    decay: number;
  }>;
}

export interface AppState {
  currentImage: ImageData | null;
  selectedRegion: string | null;
  isLoading: boolean;
  view3D: boolean;
  showGallery: boolean;
  images: ImageData[];
}

export interface Point {
  x: number;
  y: number;
}

export interface RegionEditData {
  region_id: string;
  prompt: string;
  strength?: number;
}

export interface UploadResponse {
  image_id: string;
  image_url: string;
  original_filename: string;
  width: number;
  height: number;
  analysis: AnalysisResult;
  status: string;
}

export interface HealthStatus {
  status: string;
  models: {
    stable_diffusion: boolean;
    sam: boolean;
    clip: boolean;
    depth: boolean;
  };
  error?: string;
}

export interface ExportOptions {
  format: 'gltf' | 'obj' | 'ply';
  include_textures: boolean;
  optimize: boolean;
}

export interface ExportResult {
  format: string;
  export_path: string;
  export_url: string;
  file_size: number;
}
