#!/bin/bash

# Smart Visual Generation System - Startup Script
# This script helps you get the system running quickly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Emojis
ROCKET="🚀"
CHECK="✅"
WARNING="⚠️"
ERROR="❌"
INFO="ℹ️"
GEAR="⚙️"

echo -e "${PURPLE}${ROCKET} Smart Visual Generation System${NC}"
echo -e "${PURPLE}======================================${NC}"
echo ""

# Function to print colored output
print_status() {
    echo -e "${GREEN}${CHECK} $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}${WARNING} $1${NC}"
}

print_error() {
    echo -e "${RED}${ERROR} $1${NC}"
}

print_info() {
    echo -e "${BLUE}${INFO} $1${NC}"
}

# PIDs for background processes
BACKEND_PID=""
FRONTEND_PID=""

# Check if Python is installed
check_python() {
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is not installed. Please install Python 3.9+ first."
        echo "Visit: https://www.python.org/downloads/"
        exit 1
    fi

    python_version=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
    if [[ $(echo "$python_version >= 3.9" | bc -l) -eq 0 ]]; then
        print_error "Python 3.9+ is required. Current version: $python_version"
        exit 1
    fi

    print_status "Python $python_version is installed"
}

# Check if Node.js is installed
check_nodejs() {
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ first."
        echo "Visit: https://nodejs.org/"
        exit 1
    fi

    node_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [[ $node_version -lt 18 ]]; then
        print_error "Node.js 18+ is required. Current version: v$node_version"
        exit 1
    fi

    print_status "Node.js v$node_version is installed"
}

# Check if GPU support is available
check_gpu_support() {
    if command -v nvidia-smi &> /dev/null; then
        if nvidia-smi &> /dev/null; then
            print_status "NVIDIA GPU detected - GPU acceleration will be available"
            export GPU_SUPPORT=true
        else
            print_warning "NVIDIA drivers not working properly - falling back to CPU"
            export GPU_SUPPORT=false
        fi
    else
        print_warning "No NVIDIA GPU detected - running in CPU-only mode"
        export GPU_SUPPORT=false
    fi
}

# Create environment file if it doesn't exist
setup_environment() {
    if [ ! -f .env ]; then
        print_info "Creating environment file from template..."
        cp .env.example .env
        print_warning "Please edit .env file with your API keys and configuration"
        print_info "Required: OPENAI_API_KEY, HF_TOKEN"
    else
        print_status "Environment file exists"
    fi
}

# Create necessary directories
create_directories() {
    print_info "Creating necessary directories..."
    mkdir -p static/{images,masks,depth,3d,comparisons,visualizations,metadata}
    mkdir -p models/{sam,stable_diffusion,clip,depth}
    mkdir -p logs
    print_status "Directories created"
}

# Download required models (optional)
download_models() {
    read -p "Do you want to pre-download AI models? This will take several GB of space but improve startup time. (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "Downloading models... This may take a while."
        
        # Create a temporary Python script to download models
        cat > download_models.py << 'EOF'
import os
import torch
from diffusers import StableDiffusionXLPipeline
from transformers import CLIPModel, CLIPProcessor
import urllib.request

print("🎨 Downloading Stable Diffusion XL...")
try:
    pipeline = StableDiffusionXLPipeline.from_pretrained(
        "stabilityai/stable-diffusion-xl-base-1.0",
        torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
        use_safetensors=True,
        cache_dir="./models/stable_diffusion"
    )
    print("✅ Stable Diffusion XL downloaded")
except Exception as e:
    print(f"❌ Failed to download Stable Diffusion: {e}")

print("🧠 Downloading CLIP...")
try:
    model = CLIPModel.from_pretrained("openai/clip-vit-base-patch32", cache_dir="./models/clip")
    processor = CLIPProcessor.from_pretrained("openai/clip-vit-base-patch32", cache_dir="./models/clip")
    print("✅ CLIP downloaded")
except Exception as e:
    print(f"❌ Failed to download CLIP: {e}")

print("🎯 Downloading SAM checkpoint...")
try:
    os.makedirs("./models/sam", exist_ok=True)
    sam_url = "https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth"
    sam_path = "./models/sam/sam_vit_h.pth"
    if not os.path.exists(sam_path):
        urllib.request.urlretrieve(sam_url, sam_path)
    print("✅ SAM checkpoint downloaded")
except Exception as e:
    print(f"❌ Failed to download SAM: {e}")

print("🧭 Downloading MiDaS...")
try:
    import torch
    torch.hub.load('intel-isl/MiDaS', 'MiDaS', cache_dir="./models/depth")
    print("✅ MiDaS downloaded")
except Exception as e:
    print(f"❌ Failed to download MiDaS: {e}")

print("🎉 Model download complete!")
EOF

        python3 download_models.py
        rm download_models.py
        print_status "Model download completed"
    else
        print_info "Skipping model download - models will be downloaded on first use"
    fi
}

# Install Python dependencies
install_backend_deps() {
    print_info "Installing backend dependencies..."
    cd backend

    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        print_info "Creating Python virtual environment..."
        python3 -m venv venv
    fi

    # Activate virtual environment
    source venv/bin/activate

    # Upgrade pip
    pip install --upgrade pip

    # Install dependencies
    pip install -r requirements.txt

    cd ..
    print_status "Backend dependencies installed"
}

# Install Node.js dependencies
install_frontend_deps() {
    print_info "Installing frontend dependencies..."
    cd frontend

    # Install dependencies
    npm install

    cd ..
    print_status "Frontend dependencies installed"
}

# Start backend service
start_backend() {
    print_info "Starting backend service..."
    cd backend

    # Activate virtual environment and start server
    source venv/bin/activate

    # Set environment variables
    export PYTHONPATH=$PWD

    # Start the backend in background
    python main.py &
    BACKEND_PID=$!

    cd ..
    print_status "Backend started (PID: $BACKEND_PID)"
}

# Start frontend service
start_frontend() {
    print_info "Starting frontend service..."
    cd frontend

    # Start the frontend in background
    npm start &
    FRONTEND_PID=$!

    cd ..
    print_status "Frontend started (PID: $FRONTEND_PID)"
}

# Wait for services to be ready
wait_for_services() {
    print_info "Waiting for services to be ready..."

    # Wait for backend
    echo -n "Waiting for backend"
    for i in {1..60}; do
        if curl -s http://localhost:8000/api/health > /dev/null 2>&1; then
            echo ""
            print_status "Backend is ready"
            break
        fi
        echo -n "."
        sleep 2
    done

    # Wait for frontend
    echo -n "Waiting for frontend"
    for i in {1..60}; do
        if curl -s http://localhost:3000 > /dev/null 2>&1; then
            echo ""
            print_status "Frontend is ready"
            break
        fi
        echo -n "."
        sleep 3
    done
}

# Stop services
stop_services() {
    print_info "Stopping services..."

    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        print_status "Backend stopped"
    fi

    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
        print_status "Frontend stopped"
    fi

    # Kill any remaining processes
    pkill -f "python main.py" 2>/dev/null || true
    pkill -f "npm start" 2>/dev/null || true
    pkill -f "react-scripts start" 2>/dev/null || true
}

# Cleanup function for script exit
cleanup() {
    print_info "Cleaning up..."
    stop_services
    exit 0
}

# Set trap for cleanup on script exit
trap cleanup EXIT INT TERM

# Show status and URLs
show_status() {
    echo ""
    echo -e "${CYAN}${ROCKET} Smart Visual Generation System is now running!${NC}"
    echo -e "${CYAN}================================================${NC}"
    echo ""
    echo -e "${GREEN}🌐 Frontend (Web UI):${NC} http://localhost:3000"
    echo -e "${GREEN}🔧 Backend API:${NC} http://localhost:8000"
    echo -e "${GREEN}📚 API Documentation:${NC} http://localhost:8000/docs"
    echo -e "${GREEN}📊 Alternative Docs:${NC} http://localhost:8000/redoc"
    echo ""
    echo -e "${BLUE}${INFO} Features available:${NC}"
    echo "  🎨 Stable Diffusion XL image generation"
    echo "  🧠 SAM automatic segmentation"
    echo "  🔍 CLIP content analysis"
    echo "  ✏️ Region-based editing"
    echo "  🧭 3D depth estimation"
    echo "  🎥 Interactive 3D viewer"
    echo ""
    echo -e "${YELLOW}${WARNING} First startup may take longer as models are downloaded${NC}"
    echo ""
    echo -e "${PURPLE}To stop the system: ${NC}docker-compose down"
    echo -e "${PURPLE}To view logs: ${NC}docker-compose logs -f"
    echo -e "${PURPLE}To restart: ${NC}docker-compose restart"
}

# Main execution
main() {
    echo -e "${BLUE}${GEAR} Checking prerequisites...${NC}"
    check_python
    check_nodejs
    check_gpu_support

    echo ""
    echo -e "${BLUE}${GEAR} Setting up environment...${NC}"
    setup_environment
    create_directories

    echo ""
    echo -e "${BLUE}${GEAR} Installing dependencies...${NC}"
    install_backend_deps
    install_frontend_deps

    echo ""
    download_models

    echo ""
    echo -e "${BLUE}${GEAR} Starting services...${NC}"
    start_backend
    sleep 5  # Give backend time to start
    start_frontend
    wait_for_services

    show_status

    # Keep script running
    print_info "Press Ctrl+C to stop the system"
    while true; do
        sleep 1
    done
}

# Handle script arguments
case "${1:-}" in
    "stop")
        print_info "Stopping Smart Visual Generation System..."
        stop_services
        print_status "System stopped"
        ;;
    "restart")
        print_info "Restarting Smart Visual Generation System..."
        stop_services
        sleep 2
        start_backend
        sleep 5
        start_frontend
        wait_for_services
        show_status
        ;;
    "logs")
        print_info "Showing logs... (Press Ctrl+C to exit)"
        if [ ! -z "$BACKEND_PID" ]; then
            tail -f backend/logs/*.log 2>/dev/null || echo "No backend logs found"
        fi
        ;;
    "status")
        print_info "Service Status:"
        if ps -p $BACKEND_PID > /dev/null 2>&1; then
            print_status "Backend running (PID: $BACKEND_PID)"
        else
            print_error "Backend not running"
        fi

        if ps -p $FRONTEND_PID > /dev/null 2>&1; then
            print_status "Frontend running (PID: $FRONTEND_PID)"
        else
            print_error "Frontend not running"
        fi
        ;;
    "clean")
        print_warning "This will remove all generated images and models. Are you sure? (y/N)"
        read -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -rf static/images/* static/masks/* static/depth/* static/3d/* 2>/dev/null || true
            rm -rf models/* 2>/dev/null || true
            rm -rf backend/venv 2>/dev/null || true
            rm -rf frontend/node_modules 2>/dev/null || true
            print_status "System cleaned"
        fi
        ;;
    "help"|"-h"|"--help")
        echo "Smart Visual Generation System - Startup Script"
        echo ""
        echo "Usage: $0 [command]"
        echo ""
        echo "Commands:"
        echo "  (no command)  Start the system"
        echo "  stop          Stop the system"
        echo "  restart       Restart the system"
        echo "  logs          Show system logs"
        echo "  status        Show service status"
        echo "  clean         Remove all generated data"
        echo "  help          Show this help message"
        ;;
    "")
        main
        ;;
    *)
        print_error "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
