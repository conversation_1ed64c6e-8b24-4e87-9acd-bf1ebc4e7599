#!/bin/bash

# Smart Visual Generation System - Startup Script
# This script helps you get the system running quickly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Emojis
ROCKET="🚀"
CHECK="✅"
WARNING="⚠️"
ERROR="❌"
INFO="ℹ️"
GEAR="⚙️"

echo -e "${PURPLE}${ROCKET} Smart Visual Generation System${NC}"
echo -e "${PURPLE}======================================${NC}"
echo ""

# Function to print colored output
print_status() {
    echo -e "${GREEN}${CHECK} $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}${WARNING} $1${NC}"
}

print_error() {
    echo -e "${RED}${ERROR} $1${NC}"
}

print_info() {
    echo -e "${BLUE}${INFO} $1${NC}"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        echo "Visit: https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        echo "Visit: https://docs.docker.com/compose/install/"
        exit 1
    fi
    
    print_status "Docker and Docker Compose are installed"
}

# Check if NVIDIA Docker is available (for GPU support)
check_nvidia_docker() {
    if command -v nvidia-docker &> /dev/null || docker info | grep -q nvidia; then
        print_status "NVIDIA Docker support detected - GPU acceleration will be available"
        export GPU_SUPPORT=true
    else
        print_warning "NVIDIA Docker not detected - running in CPU-only mode"
        export GPU_SUPPORT=false
    fi
}

# Create environment file if it doesn't exist
setup_environment() {
    if [ ! -f .env ]; then
        print_info "Creating environment file from template..."
        cp .env.example .env
        print_warning "Please edit .env file with your API keys and configuration"
        print_info "Required: OPENAI_API_KEY, HF_TOKEN"
    else
        print_status "Environment file exists"
    fi
}

# Create necessary directories
create_directories() {
    print_info "Creating necessary directories..."
    mkdir -p static/{images,masks,depth,3d,comparisons,visualizations,metadata}
    mkdir -p models/{sam,stable_diffusion,clip,depth}
    mkdir -p logs
    print_status "Directories created"
}

# Download required models (optional)
download_models() {
    read -p "Do you want to pre-download AI models? This will take several GB of space but improve startup time. (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "Downloading models... This may take a while."
        
        # Create a temporary Python script to download models
        cat > download_models.py << 'EOF'
import os
import torch
from diffusers import StableDiffusionXLPipeline
from transformers import CLIPModel, CLIPProcessor
import urllib.request

print("🎨 Downloading Stable Diffusion XL...")
try:
    pipeline = StableDiffusionXLPipeline.from_pretrained(
        "stabilityai/stable-diffusion-xl-base-1.0",
        torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
        use_safetensors=True,
        cache_dir="./models/stable_diffusion"
    )
    print("✅ Stable Diffusion XL downloaded")
except Exception as e:
    print(f"❌ Failed to download Stable Diffusion: {e}")

print("🧠 Downloading CLIP...")
try:
    model = CLIPModel.from_pretrained("openai/clip-vit-base-patch32", cache_dir="./models/clip")
    processor = CLIPProcessor.from_pretrained("openai/clip-vit-base-patch32", cache_dir="./models/clip")
    print("✅ CLIP downloaded")
except Exception as e:
    print(f"❌ Failed to download CLIP: {e}")

print("🎯 Downloading SAM checkpoint...")
try:
    os.makedirs("./models/sam", exist_ok=True)
    sam_url = "https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth"
    sam_path = "./models/sam/sam_vit_h.pth"
    if not os.path.exists(sam_path):
        urllib.request.urlretrieve(sam_url, sam_path)
    print("✅ SAM checkpoint downloaded")
except Exception as e:
    print(f"❌ Failed to download SAM: {e}")

print("🧭 Downloading MiDaS...")
try:
    import torch
    torch.hub.load('intel-isl/MiDaS', 'MiDaS', cache_dir="./models/depth")
    print("✅ MiDaS downloaded")
except Exception as e:
    print(f"❌ Failed to download MiDaS: {e}")

print("🎉 Model download complete!")
EOF

        python3 download_models.py
        rm download_models.py
        print_status "Model download completed"
    else
        print_info "Skipping model download - models will be downloaded on first use"
    fi
}

# Build and start services
start_services() {
    print_info "Building and starting services..."
    
    if [ "$GPU_SUPPORT" = true ]; then
        print_info "Starting with GPU support..."
        docker-compose up --build -d
    else
        print_warning "Starting in CPU-only mode..."
        # Remove GPU requirements from docker-compose for CPU-only mode
        docker-compose up --build -d
    fi
    
    print_status "Services started"
}

# Wait for services to be ready
wait_for_services() {
    print_info "Waiting for services to be ready..."
    
    # Wait for backend
    echo -n "Waiting for backend"
    for i in {1..60}; do
        if curl -s http://localhost:8000/api/health > /dev/null 2>&1; then
            echo ""
            print_status "Backend is ready"
            break
        fi
        echo -n "."
        sleep 2
    done
    
    # Wait for frontend
    echo -n "Waiting for frontend"
    for i in {1..30}; do
        if curl -s http://localhost:3000 > /dev/null 2>&1; then
            echo ""
            print_status "Frontend is ready"
            break
        fi
        echo -n "."
        sleep 2
    done
}

# Show status and URLs
show_status() {
    echo ""
    echo -e "${CYAN}${ROCKET} Smart Visual Generation System is now running!${NC}"
    echo -e "${CYAN}================================================${NC}"
    echo ""
    echo -e "${GREEN}🌐 Frontend (Web UI):${NC} http://localhost:3000"
    echo -e "${GREEN}🔧 Backend API:${NC} http://localhost:8000"
    echo -e "${GREEN}📚 API Documentation:${NC} http://localhost:8000/docs"
    echo -e "${GREEN}📊 Alternative Docs:${NC} http://localhost:8000/redoc"
    echo ""
    echo -e "${BLUE}${INFO} Features available:${NC}"
    echo "  🎨 Stable Diffusion XL image generation"
    echo "  🧠 SAM automatic segmentation"
    echo "  🔍 CLIP content analysis"
    echo "  ✏️ Region-based editing"
    echo "  🧭 3D depth estimation"
    echo "  🎥 Interactive 3D viewer"
    echo ""
    echo -e "${YELLOW}${WARNING} First startup may take longer as models are downloaded${NC}"
    echo ""
    echo -e "${PURPLE}To stop the system: ${NC}docker-compose down"
    echo -e "${PURPLE}To view logs: ${NC}docker-compose logs -f"
    echo -e "${PURPLE}To restart: ${NC}docker-compose restart"
}

# Main execution
main() {
    echo -e "${BLUE}${GEAR} Checking prerequisites...${NC}"
    check_docker
    check_nvidia_docker
    
    echo ""
    echo -e "${BLUE}${GEAR} Setting up environment...${NC}"
    setup_environment
    create_directories
    
    echo ""
    download_models
    
    echo ""
    echo -e "${BLUE}${GEAR} Starting services...${NC}"
    start_services
    wait_for_services
    
    show_status
}

# Handle script arguments
case "${1:-}" in
    "stop")
        print_info "Stopping Smart Visual Generation System..."
        docker-compose down
        print_status "System stopped"
        ;;
    "restart")
        print_info "Restarting Smart Visual Generation System..."
        docker-compose restart
        wait_for_services
        show_status
        ;;
    "logs")
        docker-compose logs -f
        ;;
    "status")
        docker-compose ps
        ;;
    "clean")
        print_warning "This will remove all containers, images, and volumes. Are you sure? (y/N)"
        read -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker-compose down -v --rmi all
            print_status "System cleaned"
        fi
        ;;
    "help"|"-h"|"--help")
        echo "Smart Visual Generation System - Startup Script"
        echo ""
        echo "Usage: $0 [command]"
        echo ""
        echo "Commands:"
        echo "  (no command)  Start the system"
        echo "  stop          Stop the system"
        echo "  restart       Restart the system"
        echo "  logs          Show system logs"
        echo "  status        Show service status"
        echo "  clean         Remove all containers and data"
        echo "  help          Show this help message"
        ;;
    "")
        main
        ;;
    *)
        print_error "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
