import React from 'react';
import { motion } from 'framer-motion';

const LoadingOverlay: React.FC = () => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
    >
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
        className="glass p-8 rounded-2xl text-center max-w-md mx-4"
      >
        {/* Animated Logo */}
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          className="text-6xl mb-4"
        >
          🎨
        </motion.div>

        {/* Loading Text */}
        <h3 className="text-xl font-semibold text-white mb-2">
          AI Magic in Progress
        </h3>
        
        <p className="text-white/70 mb-6">
          Our AI models are working hard to create something amazing...
        </p>

        {/* Progress Bar */}
        <div className="w-full bg-white/20 rounded-full h-2 mb-4">
          <motion.div
            className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
            initial={{ width: "0%" }}
            animate={{ width: "100%" }}
            transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
          />
        </div>

        {/* Loading Steps */}
        <div className="space-y-2 text-sm text-white/60">
          <motion.div
            initial={{ opacity: 0.3 }}
            animate={{ opacity: [0.3, 1, 0.3] }}
            transition={{ duration: 2, repeat: Infinity, delay: 0 }}
            className="flex items-center justify-center space-x-2"
          >
            <span>🧠</span>
            <span>Processing with AI models...</span>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0.3 }}
            animate={{ opacity: [0.3, 1, 0.3] }}
            transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
            className="flex items-center justify-center space-x-2"
          >
            <span>🎯</span>
            <span>Analyzing and segmenting...</span>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0.3 }}
            animate={{ opacity: [0.3, 1, 0.3] }}
            transition={{ duration: 2, repeat: Infinity, delay: 1 }}
            className="flex items-center justify-center space-x-2"
          >
            <span>✨</span>
            <span>Adding final touches...</span>
          </motion.div>
        </div>

        {/* Tip */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1 }}
          className="mt-6 p-3 bg-blue-500/20 rounded-lg border border-blue-400/30"
        >
          <p className="text-xs text-blue-200">
            💡 Tip: Complex images may take longer to process. The wait is worth it!
          </p>
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

export default LoadingOverlay;
