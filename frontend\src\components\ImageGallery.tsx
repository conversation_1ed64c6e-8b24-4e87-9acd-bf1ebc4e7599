import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useQuery } from 'react-query';
import toast from 'react-hot-toast';
import { ImageData } from '../types';
import { apiService } from '../services/api';

interface ImageGalleryProps {
  onClose: () => void;
  onSelectImage: (image: ImageData) => void;
}

const ImageGallery: React.FC<ImageGalleryProps> = ({ onClose, onSelectImage }) => {
  const [selectedImages, setSelectedImages] = useState<Set<string>>(new Set());
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'name'>('newest');

  // Fetch images
  const { data: images = [], isLoading, error, refetch } = useQuery(
    'gallery-images',
    () => apiService.listImages(100),
    {
      refetchOnWindowFocus: false,
    }
  );

  // Sort images
  const sortedImages = React.useMemo(() => {
    const sorted = [...images];
    switch (sortBy) {
      case 'newest':
        return sorted.sort((a, b) => 
          (b.file_info?.created || 0) - (a.file_info?.created || 0)
        );
      case 'oldest':
        return sorted.sort((a, b) => 
          (a.file_info?.created || 0) - (b.file_info?.created || 0)
        );
      case 'name':
        return sorted.sort((a, b) => 
          (a.prompt || a.original_filename || '').localeCompare(
            b.prompt || b.original_filename || ''
          )
        );
      default:
        return sorted;
    }
  }, [images, sortBy]);

  const handleImageSelect = (image: ImageData) => {
    onSelectImage(image);
    onClose();
  };

  const handleImageToggle = (imageId: string) => {
    const newSelected = new Set(selectedImages);
    if (newSelected.has(imageId)) {
      newSelected.delete(imageId);
    } else {
      newSelected.add(imageId);
    }
    setSelectedImages(newSelected);
  };

  const handleDeleteSelected = async () => {
    if (selectedImages.size === 0) {
      toast.error('No images selected');
      return;
    }

    const confirmed = window.confirm(
      `Are you sure you want to delete ${selectedImages.size} image(s)?`
    );

    if (!confirmed) return;

    try {
      await Promise.all(
        Array.from(selectedImages).map(imageId => 
          apiService.deleteImage(imageId)
        )
      );
      
      toast.success(`Deleted ${selectedImages.size} image(s)`);
      setSelectedImages(new Set());
      refetch();
    } catch (error) {
      toast.error('Failed to delete images');
    }
  };

  const handleSelectAll = () => {
    if (selectedImages.size === sortedImages.length) {
      setSelectedImages(new Set());
    } else {
      setSelectedImages(new Set(sortedImages.map(img => img.image_id)));
    }
  };

  if (error) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
      >
        <div className="glass p-8 rounded-2xl text-center max-w-md mx-4">
          <div className="text-4xl mb-4">❌</div>
          <h3 className="text-xl font-semibold text-white mb-2">
            Failed to Load Gallery
          </h3>
          <p className="text-white/70 mb-4">
            There was an error loading your images.
          </p>
          <button
            onClick={onClose}
            className="px-6 py-2 bg-white/10 hover:bg-white/20 rounded-lg text-white transition-colors"
          >
            Close
          </button>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
    >
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
        className="glass rounded-2xl w-full max-w-6xl h-full max-h-[90vh] flex flex-col"
      >
        {/* Header */}
        <div className="p-6 border-b border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-white">🖼️ Image Gallery</h2>
              <p className="text-white/70">
                {sortedImages.length} image{sortedImages.length !== 1 ? 's' : ''}
                {selectedImages.size > 0 && (
                  <span className="ml-2">
                    • {selectedImages.size} selected
                  </span>
                )}
              </p>
            </div>
            
            <button
              onClick={onClose}
              className="p-2 hover:bg-white/10 rounded-lg text-white transition-colors"
            >
              ✕
            </button>
          </div>

          {/* Controls */}
          <div className="flex items-center justify-between mt-4">
            <div className="flex items-center space-x-4">
              {/* View Mode */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-lg transition-colors ${
                    viewMode === 'grid' 
                      ? 'bg-blue-500/30 text-blue-200' 
                      : 'bg-white/10 text-white/70 hover:text-white'
                  }`}
                >
                  ⊞
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-lg transition-colors ${
                    viewMode === 'list' 
                      ? 'bg-blue-500/30 text-blue-200' 
                      : 'bg-white/10 text-white/70 hover:text-white'
                  }`}
                >
                  ☰
                </button>
              </div>

              {/* Sort */}
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="name">By Name</option>
              </select>
            </div>

            {/* Bulk Actions */}
            <div className="flex items-center space-x-2">
              <button
                onClick={handleSelectAll}
                className="px-3 py-2 bg-white/10 hover:bg-white/20 rounded-lg text-white text-sm transition-colors"
              >
                {selectedImages.size === sortedImages.length ? 'Deselect All' : 'Select All'}
              </button>
              
              {selectedImages.size > 0 && (
                <button
                  onClick={handleDeleteSelected}
                  className="px-3 py-2 bg-red-500/20 hover:bg-red-500/30 text-red-200 rounded-lg text-sm transition-colors"
                >
                  Delete Selected
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center text-white">
                <div className="loading-spinner mx-auto mb-4"></div>
                <p>Loading gallery...</p>
              </div>
            </div>
          ) : sortedImages.length === 0 ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center text-white/70">
                <div className="text-6xl mb-4">🖼️</div>
                <h3 className="text-xl font-semibold mb-2">No Images Yet</h3>
                <p>Generate or upload some images to see them here!</p>
              </div>
            </div>
          ) : viewMode === 'grid' ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              {sortedImages.map((image, index) => (
                <motion.div
                  key={image.image_id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="group relative"
                >
                  <div
                    className={`relative aspect-square rounded-lg overflow-hidden cursor-pointer border-2 transition-all ${
                      selectedImages.has(image.image_id)
                        ? 'border-blue-400 ring-2 ring-blue-400/50'
                        : 'border-transparent hover:border-white/30'
                    }`}
                    onClick={() => handleImageSelect(image)}
                  >
                    <img
                      src={image.image_url}
                      alt={image.prompt || 'Generated image'}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    
                    {/* Overlay */}
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/30 transition-colors">
                      <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                        <div className="text-white text-center">
                          <div className="text-2xl mb-1">👁️</div>
                          <p className="text-sm">View</p>
                        </div>
                      </div>
                    </div>

                    {/* Selection checkbox */}
                    <div className="absolute top-2 left-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleImageToggle(image.image_id);
                        }}
                        className={`w-6 h-6 rounded border-2 flex items-center justify-center transition-colors ${
                          selectedImages.has(image.image_id)
                            ? 'bg-blue-500 border-blue-500 text-white'
                            : 'bg-black/50 border-white/50 hover:border-white'
                        }`}
                      >
                        {selectedImages.has(image.image_id) && '✓'}
                      </button>
                    </div>

                    {/* 3D indicator */}
                    {image.three_d_data && (
                      <div className="absolute top-2 right-2 bg-green-500/80 text-white px-2 py-1 rounded text-xs">
                        3D
                      </div>
                    )}
                  </div>

                  {/* Image info */}
                  <div className="mt-2">
                    <p className="text-white text-sm font-medium truncate">
                      {image.prompt || image.original_filename || 'Untitled'}
                    </p>
                    <p className="text-white/50 text-xs">
                      {image.width} × {image.height}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="space-y-3">
              {sortedImages.map((image, index) => (
                <motion.div
                  key={image.image_id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.02 }}
                  className={`flex items-center space-x-4 p-4 rounded-lg cursor-pointer transition-all ${
                    selectedImages.has(image.image_id)
                      ? 'bg-blue-500/20 border border-blue-400/50'
                      : 'bg-white/5 hover:bg-white/10'
                  }`}
                  onClick={() => handleImageSelect(image)}
                >
                  {/* Thumbnail */}
                  <div className="w-16 h-16 rounded-lg overflow-hidden flex-shrink-0">
                    <img
                      src={image.image_url}
                      alt={image.prompt || 'Generated image'}
                      className="w-full h-full object-cover"
                    />
                  </div>

                  {/* Info */}
                  <div className="flex-1 min-w-0">
                    <h4 className="text-white font-medium truncate">
                      {image.prompt || image.original_filename || 'Untitled'}
                    </h4>
                    <p className="text-white/70 text-sm">
                      {image.width} × {image.height}
                      {image.file_info?.created && (
                        <span className="ml-2">
                          • {new Date(image.file_info.created * 1000).toLocaleDateString()}
                        </span>
                      )}
                    </p>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2">
                    {image.three_d_data && (
                      <span className="px-2 py-1 bg-green-500/20 text-green-200 rounded text-xs">
                        3D
                      </span>
                    )}
                    
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleImageToggle(image.image_id);
                      }}
                      className={`w-6 h-6 rounded border-2 flex items-center justify-center transition-colors ${
                        selectedImages.has(image.image_id)
                          ? 'bg-blue-500 border-blue-500 text-white'
                          : 'bg-transparent border-white/50 hover:border-white'
                      }`}
                    >
                      {selectedImages.has(image.image_id) && '✓'}
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </motion.div>
    </motion.div>
  );
};

export default ImageGallery;
