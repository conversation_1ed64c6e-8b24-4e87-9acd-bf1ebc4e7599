"""
3D Service - Convert 2D images to 3D scenes
"""

import os
import json
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class ThreeDService:
    """Service for 3D scene generation and management"""
    
    def __init__(self, depth_model):
        self.depth_model = depth_model
        
        # Storage paths
        self.three_d_dir = "static/3d"
        os.makedirs(self.three_d_dir, exist_ok=True)
        
        logger.info("🧭 3D Service initialized")
    
    async def generate_3d_scene(self, image_id: str) -> Dict[str, Any]:
        """Generate 3D scene data from a 2D image"""
        
        try:
            logger.info(f"🧭 Generating 3D scene for image: {image_id}")
            
            # Load image
            from PIL import Image
            image_path = f"static/images/{image_id}.png"
            if not os.path.exists(image_path):
                raise FileNotFoundError(f"Image {image_id} not found")
            
            image = Image.open(image_path)
            
            # Generate depth map
            depth_result = await self.depth_model.estimate_depth(image)
            
            # Create 3D scene configuration
            scene_config = self._create_scene_config(image, depth_result)
            
            # Generate camera settings
            camera_settings = self._generate_camera_settings(image.size)
            
            # Create lighting setup
            lighting_setup = self._create_lighting_setup()
            
            # Save 3D scene data
            scene_data = {
                "image_id": image_id,
                "depth_data": depth_result,
                "scene_config": scene_config,
                "camera_settings": camera_settings,
                "lighting_setup": lighting_setup,
                "metadata": {
                    "image_dimensions": image.size,
                    "depth_quality": depth_result.get("depth_stats", {}),
                    "generation_timestamp": self._get_timestamp()
                }
            }
            
            # Save scene data
            scene_path = f"{self.three_d_dir}/{image_id}_scene.json"
            with open(scene_path, 'w') as f:
                json.dump(scene_data, f, indent=2, default=str)
            
            return scene_data
            
        except Exception as e:
            logger.error(f"❌ 3D scene generation failed: {e}")
            raise
    
    def _create_scene_config(self, image, depth_result: Dict[str, Any]) -> Dict[str, Any]:
        """Create 3D scene configuration"""
        
        three_d_data = depth_result.get("three_d_data", {})
        
        return {
            "geometry": {
                "vertices": three_d_data.get("vertices", []),
                "colors": three_d_data.get("colors", []),
                "faces": three_d_data.get("faces", []),
                "vertex_count": len(three_d_data.get("vertices", [])),
                "face_count": len(three_d_data.get("faces", []))
            },
            "textures": {
                "diffuse_map": f"/static/images/{depth_result.get('depth_id', 'unknown')}.png",
                "depth_map": depth_result.get("depth_map_url", ""),
                "depth_visualization": depth_result.get("depth_viz_url", "")
            },
            "materials": {
                "base_material": {
                    "type": "MeshStandardMaterial",
                    "roughness": 0.7,
                    "metalness": 0.1,
                    "transparent": False
                }
            },
            "animations": {
                "parallax_enabled": True,
                "rotation_enabled": True,
                "zoom_enabled": True,
                "auto_rotate": False,
                "rotation_speed": 0.01
            }
        }
    
    def _generate_camera_settings(self, image_size: tuple) -> Dict[str, Any]:
        """Generate optimal camera settings for the 3D scene"""
        
        width, height = image_size
        aspect_ratio = width / height
        
        return {
            "type": "PerspectiveCamera",
            "fov": 75,
            "aspect": aspect_ratio,
            "near": 0.1,
            "far": 1000,
            "position": {
                "x": 0,
                "y": 0,
                "z": 2
            },
            "target": {
                "x": 0,
                "y": 0,
                "z": 0
            },
            "controls": {
                "enabled": True,
                "enableDamping": True,
                "dampingFactor": 0.05,
                "enableZoom": True,
                "enableRotate": True,
                "enablePan": True,
                "maxDistance": 10,
                "minDistance": 0.5,
                "maxPolarAngle": 3.14159,  # Math.PI
                "minPolarAngle": 0
            }
        }
    
    def _create_lighting_setup(self) -> Dict[str, Any]:
        """Create lighting configuration for the 3D scene"""
        
        return {
            "ambient_light": {
                "type": "AmbientLight",
                "color": 0x404040,
                "intensity": 0.4
            },
            "directional_light": {
                "type": "DirectionalLight",
                "color": 0xffffff,
                "intensity": 0.8,
                "position": {
                    "x": 5,
                    "y": 5,
                    "z": 5
                },
                "cast_shadow": True,
                "shadow": {
                    "map_size": 2048,
                    "camera": {
                        "near": 0.1,
                        "far": 50,
                        "left": -10,
                        "right": 10,
                        "top": 10,
                        "bottom": -10
                    }
                }
            },
            "point_lights": [
                {
                    "type": "PointLight",
                    "color": 0xffffff,
                    "intensity": 0.3,
                    "position": {
                        "x": -5,
                        "y": 3,
                        "z": 2
                    },
                    "distance": 20,
                    "decay": 2
                }
            ]
        }
    
    async def update_scene_settings(
        self,
        image_id: str,
        settings: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Update 3D scene settings"""
        
        try:
            logger.info(f"🔧 Updating 3D scene settings for: {image_id}")
            
            # Load existing scene data
            scene_path = f"{self.three_d_dir}/{image_id}_scene.json"
            if not os.path.exists(scene_path):
                raise FileNotFoundError(f"3D scene for {image_id} not found")
            
            with open(scene_path, 'r') as f:
                scene_data = json.load(f)
            
            # Update settings
            if "camera" in settings:
                scene_data["camera_settings"].update(settings["camera"])
            
            if "lighting" in settings:
                scene_data["lighting_setup"].update(settings["lighting"])
            
            if "animations" in settings:
                scene_data["scene_config"]["animations"].update(settings["animations"])
            
            # Save updated scene data
            with open(scene_path, 'w') as f:
                json.dump(scene_data, f, indent=2, default=str)
            
            return scene_data
            
        except Exception as e:
            logger.error(f"❌ Failed to update scene settings: {e}")
            raise
    
    async def export_scene(
        self,
        image_id: str,
        format: str = "gltf"
    ) -> Dict[str, Any]:
        """Export 3D scene in various formats"""
        
        try:
            logger.info(f"📤 Exporting 3D scene for: {image_id} as {format}")
            
            # Load scene data
            scene_path = f"{self.three_d_dir}/{image_id}_scene.json"
            if not os.path.exists(scene_path):
                raise FileNotFoundError(f"3D scene for {image_id} not found")
            
            with open(scene_path, 'r') as f:
                scene_data = json.load(f)
            
            # Export based on format
            if format.lower() == "gltf":
                export_result = await self._export_gltf(image_id, scene_data)
            elif format.lower() == "obj":
                export_result = await self._export_obj(image_id, scene_data)
            elif format.lower() == "ply":
                export_result = await self._export_ply(image_id, scene_data)
            else:
                raise ValueError(f"Unsupported export format: {format}")
            
            return export_result
            
        except Exception as e:
            logger.error(f"❌ Scene export failed: {e}")
            raise
    
    async def _export_gltf(self, image_id: str, scene_data: Dict[str, Any]) -> Dict[str, Any]:
        """Export scene as GLTF format"""
        
        # This would require a proper GLTF exporter
        # For now, return the scene data in a GLTF-compatible structure
        
        export_path = f"{self.three_d_dir}/{image_id}_scene.gltf"
        
        gltf_data = {
            "asset": {
                "version": "2.0",
                "generator": "Smart Visual Generation System"
            },
            "scene": 0,
            "scenes": [
                {
                    "nodes": [0]
                }
            ],
            "nodes": [
                {
                    "mesh": 0
                }
            ],
            "meshes": [
                {
                    "primitives": [
                        {
                            "attributes": {
                                "POSITION": 0,
                                "COLOR_0": 1
                            },
                            "indices": 2
                        }
                    ]
                }
            ]
        }
        
        with open(export_path, 'w') as f:
            json.dump(gltf_data, f, indent=2)
        
        return {
            "format": "gltf",
            "export_path": export_path,
            "export_url": f"/static/3d/{image_id}_scene.gltf",
            "file_size": os.path.getsize(export_path)
        }
    
    async def _export_obj(self, image_id: str, scene_data: Dict[str, Any]) -> Dict[str, Any]:
        """Export scene as OBJ format"""
        
        export_path = f"{self.three_d_dir}/{image_id}_scene.obj"
        
        # Extract geometry data
        geometry = scene_data.get("scene_config", {}).get("geometry", {})
        vertices = geometry.get("vertices", [])
        faces = geometry.get("faces", [])
        
        # Write OBJ file
        with open(export_path, 'w') as f:
            f.write("# Smart Visual Generation System OBJ Export\n")
            
            # Write vertices
            for vertex in vertices:
                f.write(f"v {vertex[0]} {vertex[1]} {vertex[2]}\n")
            
            # Write faces (OBJ uses 1-based indexing)
            for face in faces:
                f.write(f"f {face[0]+1} {face[1]+1} {face[2]+1}\n")
        
        return {
            "format": "obj",
            "export_path": export_path,
            "export_url": f"/static/3d/{image_id}_scene.obj",
            "file_size": os.path.getsize(export_path)
        }
    
    async def _export_ply(self, image_id: str, scene_data: Dict[str, Any]) -> Dict[str, Any]:
        """Export scene as PLY format"""
        
        export_path = f"{self.three_d_dir}/{image_id}_scene.ply"
        
        # Extract geometry data
        geometry = scene_data.get("scene_config", {}).get("geometry", {})
        vertices = geometry.get("vertices", [])
        colors = geometry.get("colors", [])
        
        # Write PLY file
        with open(export_path, 'w') as f:
            f.write("ply\n")
            f.write("format ascii 1.0\n")
            f.write(f"element vertex {len(vertices)}\n")
            f.write("property float x\n")
            f.write("property float y\n")
            f.write("property float z\n")
            f.write("property uchar red\n")
            f.write("property uchar green\n")
            f.write("property uchar blue\n")
            f.write("end_header\n")
            
            # Write vertex data
            for i, vertex in enumerate(vertices):
                color = colors[i] if i < len(colors) else [1.0, 1.0, 1.0]
                r, g, b = [int(c * 255) for c in color[:3]]
                f.write(f"{vertex[0]} {vertex[1]} {vertex[2]} {r} {g} {b}\n")
        
        return {
            "format": "ply",
            "export_path": export_path,
            "export_url": f"/static/3d/{image_id}_scene.ply",
            "file_size": os.path.getsize(export_path)
        }
    
    def _get_timestamp(self) -> str:
        """Get current timestamp"""
        from datetime import datetime
        return datetime.now().isoformat()
