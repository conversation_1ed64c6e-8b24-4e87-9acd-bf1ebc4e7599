{"name": "smart-visual-generation-frontend", "version": "1.0.0", "description": "Frontend for Smart Visual Generation System with Semantic Editing and 3D Enhancement", "private": true, "dependencies": {"@types/node": "^20.8.0", "@types/react": "^18.2.25", "@types/react-dom": "^18.2.11", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^5.2.2", "web-vitals": "^3.4.0", "@heroicons/react": "^2.0.18", "@headlessui/react": "^1.7.17", "clsx": "^2.0.0", "tailwindcss": "^3.3.5", "@tailwindcss/forms": "^0.5.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "three": "^0.157.0", "@types/three": "^0.157.0", "@react-three/fiber": "^8.15.11", "@react-three/drei": "^9.88.13", "axios": "^1.5.1", "react-query": "^3.39.3", "zustand": "^4.4.4", "fabric": "^5.3.0", "@types/fabric": "^5.3.0", "react-dropzone": "^14.2.3", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.4", "canvas-confetti": "^1.6.0", "@types/canvas-confetti": "^1.6.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,css,md}"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/jest": "^29.5.5", "eslint": "^8.51.0", "prettier": "^3.0.3"}, "proxy": "http://localhost:8000"}