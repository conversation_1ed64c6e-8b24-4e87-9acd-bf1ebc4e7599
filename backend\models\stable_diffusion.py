"""
Stable Diffusion XL Model Integration
High-quality image generation from text prompts
"""

import os
import torch
import logging
from typing import Optional, Dict, Any
from PIL import Image
import numpy as np

from diffusers import (
    StableDiffusionXLPipeline,
    StableDiffusionXLInpaintPipeline,
    DPMSolverMultistepScheduler
)

logger = logging.getLogger(__name__)

class StableDiffusionModel:
    """Stable Diffusion XL model for image generation and inpainting"""
    
    def __init__(self):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.pipeline = None
        self.inpaint_pipeline = None
        self.is_loaded = False
        
        # Model configuration
        self.model_id = "stabilityai/stable-diffusion-xl-base-1.0"
        self.refiner_id = "stabilityai/stable-diffusion-xl-refiner-1.0"
        
        logger.info(f"🎨 Initializing Stable Diffusion on {self.device}")
        self._load_models()
    
    def _load_models(self):
        """Load the Stable Diffusion models"""
        try:
            # Load base pipeline
            logger.info("📦 Loading SDXL base model...")
            self.pipeline = StableDiffusionXLPipeline.from_pretrained(
                self.model_id,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                use_safetensors=True,
                variant="fp16" if self.device == "cuda" else None
            )
            
            # Load inpainting pipeline
            logger.info("📦 Loading SDXL inpainting model...")
            self.inpaint_pipeline = StableDiffusionXLInpaintPipeline.from_pretrained(
                "diffusers/stable-diffusion-xl-1.0-inpainting-0.1",
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                use_safetensors=True,
                variant="fp16" if self.device == "cuda" else None
            )
            
            # Move to device
            self.pipeline = self.pipeline.to(self.device)
            self.inpaint_pipeline = self.inpaint_pipeline.to(self.device)
            
            # Optimize for memory
            if self.device == "cuda":
                self.pipeline.enable_model_cpu_offload()
                self.inpaint_pipeline.enable_model_cpu_offload()
                self.pipeline.enable_vae_slicing()
                self.inpaint_pipeline.enable_vae_slicing()
            
            # Use faster scheduler
            self.pipeline.scheduler = DPMSolverMultistepScheduler.from_config(
                self.pipeline.scheduler.config
            )
            self.inpaint_pipeline.scheduler = DPMSolverMultistepScheduler.from_config(
                self.inpaint_pipeline.scheduler.config
            )
            
            self.is_loaded = True
            logger.info("✅ Stable Diffusion models loaded successfully!")
            
        except Exception as e:
            logger.error(f"❌ Failed to load Stable Diffusion: {e}")
            self.is_loaded = False
            raise
    
    async def generate_image(
        self,
        prompt: str,
        negative_prompt: Optional[str] = None,
        width: int = 1024,
        height: int = 1024,
        num_inference_steps: int = 50,
        guidance_scale: float = 7.5,
        seed: Optional[int] = None
    ) -> Dict[str, Any]:
        """Generate an image from a text prompt"""
        
        if not self.is_loaded:
            raise RuntimeError("Stable Diffusion model not loaded")
        
        try:
            # Set random seed for reproducibility
            if seed is not None:
                torch.manual_seed(seed)
                if torch.cuda.is_available():
                    torch.cuda.manual_seed(seed)
            
            logger.info(f"🎨 Generating: {prompt[:50]}...")
            
            # Generate image
            with torch.autocast(self.device):
                result = self.pipeline(
                    prompt=prompt,
                    negative_prompt=negative_prompt,
                    width=width,
                    height=height,
                    num_inference_steps=num_inference_steps,
                    guidance_scale=guidance_scale,
                    output_type="pil"
                )
            
            image = result.images[0]
            
            # Save image
            image_id = self._generate_image_id()
            image_path = f"static/images/{image_id}.png"
            os.makedirs(os.path.dirname(image_path), exist_ok=True)
            image.save(image_path)
            
            return {
                "image_id": image_id,
                "image_url": f"/static/images/{image_id}.png",
                "prompt": prompt,
                "negative_prompt": negative_prompt,
                "width": width,
                "height": height,
                "seed": seed,
                "guidance_scale": guidance_scale,
                "num_inference_steps": num_inference_steps
            }
            
        except Exception as e:
            logger.error(f"❌ Image generation failed: {e}")
            raise
    
    async def inpaint_region(
        self,
        image: Image.Image,
        mask: Image.Image,
        prompt: str,
        negative_prompt: Optional[str] = None,
        strength: float = 0.8,
        guidance_scale: float = 7.5,
        num_inference_steps: int = 50
    ) -> Image.Image:
        """Inpaint a specific region of an image"""
        
        if not self.is_loaded:
            raise RuntimeError("Stable Diffusion inpainting model not loaded")
        
        try:
            logger.info(f"✏️ Inpainting region: {prompt[:50]}...")
            
            # Ensure images are the right size
            image = image.resize((1024, 1024))
            mask = mask.resize((1024, 1024))
            
            with torch.autocast(self.device):
                result = self.inpaint_pipeline(
                    prompt=prompt,
                    negative_prompt=negative_prompt,
                    image=image,
                    mask_image=mask,
                    strength=strength,
                    guidance_scale=guidance_scale,
                    num_inference_steps=num_inference_steps,
                    output_type="pil"
                )
            
            return result.images[0]
            
        except Exception as e:
            logger.error(f"❌ Inpainting failed: {e}")
            raise
    
    def _generate_image_id(self) -> str:
        """Generate a unique image ID"""
        import uuid
        return str(uuid.uuid4())
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model"""
        return {
            "model_id": self.model_id,
            "device": self.device,
            "is_loaded": self.is_loaded,
            "memory_usage": self._get_memory_usage()
        }
    
    def _get_memory_usage(self) -> Dict[str, float]:
        """Get current GPU memory usage"""
        if self.device == "cuda" and torch.cuda.is_available():
            return {
                "allocated_gb": torch.cuda.memory_allocated() / 1024**3,
                "reserved_gb": torch.cuda.memory_reserved() / 1024**3,
                "max_allocated_gb": torch.cuda.max_memory_allocated() / 1024**3
            }
        return {"cpu_memory": "N/A"}
    
    def clear_cache(self):
        """Clear GPU memory cache"""
        if self.device == "cuda" and torch.cuda.is_available():
            torch.cuda.empty_cache()
            logger.info("🧹 GPU cache cleared")
