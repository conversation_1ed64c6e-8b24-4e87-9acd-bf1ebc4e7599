"""
Image Service - Core image processing and management
"""

import os
import json
import logging
import uuid
import io
from typing import Dict, Any, Optional, List
from PIL import Image
from fastapi import UploadFile

logger = logging.getLogger(__name__)

class ImageService:
    """Core service for image generation, analysis, and management"""
    
    def __init__(self, sd_model, sam_model, clip_model):
        self.sd_model = sd_model
        self.sam_model = sam_model
        self.clip_model = clip_model
        
        # Storage paths
        self.images_dir = "static/images"
        self.metadata_dir = "static/metadata"
        
        # Ensure directories exist
        os.makedirs(self.images_dir, exist_ok=True)
        os.makedirs(self.metadata_dir, exist_ok=True)
        
        logger.info("🖼️ Image Service initialized")
    
    async def generate_image(
        self,
        prompt: str,
        negative_prompt: Optional[str] = None,
        width: int = 1024,
        height: int = 1024,
        num_inference_steps: int = 50,
        guidance_scale: float = 7.5,
        seed: Optional[int] = None
    ) -> Dict[str, Any]:
        """Generate a new image and analyze it"""
        
        try:
            # Generate image using Stable Diffusion
            generation_result = await self.sd_model.generate_image(
                prompt=prompt,
                negative_prompt=negative_prompt,
                width=width,
                height=height,
                num_inference_steps=num_inference_steps,
                guidance_scale=guidance_scale,
                seed=seed
            )
            
            image_id = generation_result["image_id"]
            
            # Load the generated image for analysis
            image_path = f"{self.images_dir}/{image_id}.png"
            image = Image.open(image_path)
            
            # Automatically analyze the image
            analysis_result = await self._analyze_image_content(image, image_id)
            
            # Combine results
            result = {
                **generation_result,
                "analysis": analysis_result,
                "status": "generated_and_analyzed"
            }
            
            # Save metadata
            await self._save_image_metadata(image_id, result)
            
            logger.info(f"✅ Generated and analyzed image: {image_id}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Image generation failed: {e}")
            raise
    
    async def upload_image(self, file: UploadFile) -> Dict[str, Any]:
        """Upload and analyze an existing image"""
        
        try:
            # Generate unique image ID
            image_id = str(uuid.uuid4())
            
            # Save uploaded file
            image_path = f"{self.images_dir}/{image_id}.png"
            
            # Read and save image
            content = await file.read()
            image = Image.open(io.BytesIO(content))
            
            # Convert to RGB if necessary
            if image.mode in ('RGBA', 'LA'):
                background = Image.new('RGB', image.size, (255, 255, 255))
                background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                image = background
            elif image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Resize if too large
            max_size = 1024
            if max(image.size) > max_size:
                image.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)
            
            image.save(image_path)
            
            # Analyze the uploaded image
            analysis_result = await self._analyze_image_content(image, image_id)
            
            result = {
                "image_id": image_id,
                "image_url": f"/static/images/{image_id}.png",
                "original_filename": file.filename,
                "width": image.width,
                "height": image.height,
                "analysis": analysis_result,
                "status": "uploaded_and_analyzed"
            }
            
            # Save metadata
            await self._save_image_metadata(image_id, result)
            
            logger.info(f"✅ Uploaded and analyzed image: {image_id}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Image upload failed: {e}")
            raise
    
    async def analyze_image(self, image_id: str) -> Dict[str, Any]:
        """Analyze an existing image"""
        
        try:
            # Load image
            image_path = f"{self.images_dir}/{image_id}.png"
            if not os.path.exists(image_path):
                raise FileNotFoundError(f"Image {image_id} not found")
            
            image = Image.open(image_path)
            
            # Perform analysis
            analysis_result = await self._analyze_image_content(image, image_id)
            
            # Update metadata
            metadata = await self._load_image_metadata(image_id)
            if metadata:
                metadata["analysis"] = analysis_result
                await self._save_image_metadata(image_id, metadata)
            
            logger.info(f"✅ Analyzed image: {image_id}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"❌ Image analysis failed: {e}")
            raise
    
    async def _analyze_image_content(self, image: Image.Image, image_id: str) -> Dict[str, Any]:
        """Perform comprehensive image analysis"""
        
        analysis_result = {
            "image_id": image_id,
            "dimensions": {"width": image.width, "height": image.height}
        }
        
        try:
            # Segment the image using SAM
            logger.info("🔍 Segmenting image with SAM...")
            segmentation_result = await self.sam_model.segment_image(image)
            analysis_result["segmentation"] = segmentation_result
            
        except Exception as e:
            logger.warning(f"⚠️ SAM segmentation failed: {e}")
            analysis_result["segmentation"] = {"error": str(e)}
        
        try:
            # Analyze content with CLIP
            logger.info("🧠 Analyzing content with CLIP...")
            clip_result = await self.clip_model.analyze_image(image, segmentation_result.get("masks", []))
            analysis_result["content_analysis"] = clip_result
            
        except Exception as e:
            logger.warning(f"⚠️ CLIP analysis failed: {e}")
            analysis_result["content_analysis"] = {"error": str(e)}
        
        return analysis_result
    
    async def get_image_info(self, image_id: str) -> Optional[Dict[str, Any]]:
        """Get comprehensive information about an image"""
        
        try:
            # Load metadata
            metadata = await self._load_image_metadata(image_id)
            
            if not metadata:
                return None
            
            # Check if image file exists
            image_path = f"{self.images_dir}/{image_id}.png"
            if not os.path.exists(image_path):
                return None
            
            # Add file info
            stat = os.stat(image_path)
            metadata["file_info"] = {
                "size_bytes": stat.st_size,
                "created": stat.st_ctime,
                "modified": stat.st_mtime
            }
            
            return metadata
            
        except Exception as e:
            logger.error(f"❌ Failed to get image info: {e}")
            return None
    
    async def _save_image_metadata(self, image_id: str, metadata: Dict[str, Any]):
        """Save image metadata to disk"""
        
        try:
            metadata_path = f"{self.metadata_dir}/{image_id}.json"
            
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2, default=str)
            
        except Exception as e:
            logger.error(f"❌ Failed to save metadata: {e}")
    
    async def _load_image_metadata(self, image_id: str) -> Optional[Dict[str, Any]]:
        """Load image metadata from disk"""
        
        try:
            metadata_path = f"{self.metadata_dir}/{image_id}.json"
            
            if not os.path.exists(metadata_path):
                return None
            
            with open(metadata_path, 'r') as f:
                return json.load(f)
            
        except Exception as e:
            logger.error(f"❌ Failed to load metadata: {e}")
            return None
    
    async def list_images(self, limit: int = 50) -> List[Dict[str, Any]]:
        """List all images with their metadata"""
        
        try:
            images = []
            
            # Get all image files
            for filename in os.listdir(self.images_dir):
                if filename.endswith('.png'):
                    image_id = filename[:-4]  # Remove .png extension
                    
                    metadata = await self._load_image_metadata(image_id)
                    if metadata:
                        images.append(metadata)
            
            # Sort by creation time (newest first)
            images.sort(key=lambda x: x.get('file_info', {}).get('created', 0), reverse=True)
            
            return images[:limit]
            
        except Exception as e:
            logger.error(f"❌ Failed to list images: {e}")
            return []
    
    async def delete_image(self, image_id: str) -> bool:
        """Delete an image and its associated data"""
        
        try:
            # Delete image file
            image_path = f"{self.images_dir}/{image_id}.png"
            if os.path.exists(image_path):
                os.remove(image_path)
            
            # Delete metadata
            metadata_path = f"{self.metadata_dir}/{image_id}.json"
            if os.path.exists(metadata_path):
                os.remove(metadata_path)
            
            # Delete associated masks and visualizations
            mask_pattern = f"static/masks/*{image_id}*"
            import glob
            for mask_file in glob.glob(mask_pattern):
                os.remove(mask_file)
            
            logger.info(f"🗑️ Deleted image: {image_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to delete image: {e}")
            return False
