import React, { useState, useEffect } from 'react';
import { QueryClient, QueryClientProvider } from 'react-query';
import { Toaster } from 'react-hot-toast';
import { motion, AnimatePresence } from 'framer-motion';

// Components
import Header from './components/Header';
import PromptInput from './components/PromptInput';
import ImageCanvas from './components/ImageCanvas';
import RegionSelector from './components/RegionSelector';
import ThreeDViewer from './components/ThreeDViewer';
import ImageGallery from './components/ImageGallery';
import LoadingOverlay from './components/LoadingOverlay';

// Services
import { apiService } from './services/api';

// Types
import { ImageData, GenerationRequest, AnalysisResult } from './types';

// Store
import { useAppStore } from './store/appStore';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
    },
  },
});

function App() {
  const {
    currentImage,
    setCurrentImage,
    selectedRegion,
    setSelectedRegion,
    isLoading,
    setIsLoading,
    view3D,
    setView3D,
    showGallery,
    setShowGallery
  } = useAppStore();

  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);

  // Handle image generation
  const handleGenerate = async (request: GenerationRequest) => {
    try {
      setIsLoading(true);
      setCurrentImage(null);
      setAnalysisResult(null);

      const result = await apiService.generateImage(request);
      
      setCurrentImage(result);
      if (result.analysis) {
        setAnalysisResult(result.analysis);
      }
    } catch (error) {
      console.error('Generation failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle image upload
  const handleUpload = async (file: File) => {
    try {
      setIsLoading(true);
      setCurrentImage(null);
      setAnalysisResult(null);

      const result = await apiService.uploadImage(file);
      
      setCurrentImage(result);
      if (result.analysis) {
        setAnalysisResult(result.analysis);
      }
    } catch (error) {
      console.error('Upload failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle region editing
  const handleRegionEdit = async (regionId: string, newPrompt: string) => {
    if (!currentImage) return;

    try {
      setIsLoading(true);

      const result = await apiService.editRegion({
        image_id: currentImage.image_id,
        region_id: regionId,
        new_prompt: newPrompt,
        strength: 0.8
      });

      // Update current image with edited version
      setCurrentImage(result);
      setSelectedRegion(null);
    } catch (error) {
      console.error('Region editing failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle 3D generation
  const handle3DGeneration = async () => {
    if (!currentImage) return;

    try {
      setIsLoading(true);

      const result = await apiService.generate3D(currentImage.image_id);
      
      // Update current image with 3D data
      setCurrentImage({
        ...currentImage,
        three_d_data: result
      });
      
      setView3D(true);
    } catch (error) {
      console.error('3D generation failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <QueryClientProvider client={queryClient}>
      <div className="min-h-screen bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500">
        <Toaster 
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: 'rgba(255, 255, 255, 0.9)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              borderRadius: '12px',
            },
          }}
        />

        {/* Loading Overlay */}
        <AnimatePresence>
          {isLoading && <LoadingOverlay />}
        </AnimatePresence>

        {/* Header */}
        <Header 
          onToggleGallery={() => setShowGallery(!showGallery)}
          onToggle3D={() => setView3D(!view3D)}
          show3D={!!currentImage?.three_d_data}
        />

        {/* Main Content */}
        <main className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            
            {/* Left Panel - Controls */}
            <div className="lg:col-span-1 space-y-6">
              
              {/* Prompt Input */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <PromptInput 
                  onGenerate={handleGenerate}
                  onUpload={handleUpload}
                  disabled={isLoading}
                />
              </motion.div>

              {/* Region Selector */}
              <AnimatePresence>
                {analysisResult && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                  >
                    <RegionSelector
                      analysisResult={analysisResult}
                      selectedRegion={selectedRegion}
                      onSelectRegion={setSelectedRegion}
                      onEditRegion={handleRegionEdit}
                      disabled={isLoading}
                    />
                  </motion.div>
                )}
              </AnimatePresence>

              {/* 3D Controls */}
              <AnimatePresence>
                {currentImage && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                    className="glass p-6"
                  >
                    <h3 className="text-lg font-semibold text-white mb-4">
                      🧭 3D Enhancement
                    </h3>
                    
                    {!currentImage.three_d_data ? (
                      <button
                        onClick={handle3DGeneration}
                        disabled={isLoading}
                        className="w-full btn-primary text-white py-3 px-6 rounded-lg font-medium disabled:opacity-50"
                      >
                        Generate 3D Scene
                      </button>
                    ) : (
                      <div className="space-y-3">
                        <p className="text-green-200 text-sm">
                          ✅ 3D scene ready!
                        </p>
                        <button
                          onClick={() => setView3D(!view3D)}
                          className="w-full bg-green-500 hover:bg-green-600 text-white py-3 px-6 rounded-lg font-medium transition-colors"
                        >
                          {view3D ? 'Show 2D View' : 'Show 3D View'}
                        </button>
                      </div>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Center Panel - Main Canvas */}
            <div className="lg:col-span-2">
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="glass p-6 h-full"
              >
                {!currentImage ? (
                  // Welcome State
                  <div className="flex items-center justify-center h-96 text-center">
                    <div>
                      <div className="text-6xl mb-4">🎨</div>
                      <h2 className="text-2xl font-bold text-white mb-2">
                        Smart Visual Generation System
                      </h2>
                      <p className="text-white/70">
                        Generate, edit, and enhance images with AI
                      </p>
                    </div>
                  </div>
                ) : view3D && currentImage.three_d_data ? (
                  // 3D Viewer
                  <ThreeDViewer 
                    imageData={currentImage}
                    threeDData={currentImage.three_d_data}
                  />
                ) : (
                  // 2D Canvas
                  <ImageCanvas
                    imageData={currentImage}
                    analysisResult={analysisResult}
                    selectedRegion={selectedRegion}
                    onSelectRegion={setSelectedRegion}
                  />
                )}
              </motion.div>
            </div>
          </div>

          {/* Gallery Modal */}
          <AnimatePresence>
            {showGallery && (
              <ImageGallery
                onClose={() => setShowGallery(false)}
                onSelectImage={(image) => {
                  setCurrentImage(image);
                  setShowGallery(false);
                  if (image.analysis) {
                    setAnalysisResult(image.analysis);
                  }
                }}
              />
            )}
          </AnimatePresence>
        </main>
      </div>
    </QueryClientProvider>
  );
}

export default App;
